package trading_section

import (
	"app_service/gen/tool"

	"github.com/golang/mock/mockgen/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func genModel() {
	dsn := "root:nnA6MXpPQhqkxxEBLo@tcp(localhost:3306)/app_service?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	g := gen.NewGenerator(gen.Config{
		OutPath:          "./apps/business/trading_section/dal/query",
		Mode:             gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldWithTypeTag: true,
	})
	g.UseDB(db)

	isDelType := gen.FieldType("is_del", "soft_delete.DeletedAt")
	isDelFlag := gen.FieldGORMTag("is_del", func(tag field.GormTag) field.GormTag {
		return tag.Append("softDelete", "flag")
	})
	g.WithOpts(isDelType, isDelFlag)

	dataTypeMap := map[string]func(gorm.ColumnType) (dataType string){
		"datetime": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*time.Time"
			}
			return "time.Time"
		},
		"json": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*datatypes.JSON"
			}
			return "datatypes.JSON"
		},
	}
	g.WithDataTypeMap(dataTypeMap)

	ts := g.GenerateModelAs("trading_section", "TradingSection")
	tsi := g.GenerateModelAs("trading_section_item", "TradingSectionItem")
	tsds := g.GenerateModelAs("trading_section_daily_stat", "TradingSectionDailyStat")
	ats := g.GenerateModelAs("ann_trading_section", "AnnTradingSection")
	oats := g.GenerateModelAs("oper_ann_trading_section", "OperAnnTradingSection")
	mcts := g.GenerateModelAs("market_changes_trading_section", "MarketChangesTradingSection")
	applyBasic := make([]interface{}, 0)
	applyBasic = append(
		applyBasic, ts, tsi, tsds, ats, oats, mcts,
	)
	g.ApplyBasic(applyBasic...)
	g.ApplyInterface(func(method model.Method) {}, applyBasic...)
	g.Execute()
}

func Gen() {
	genModel()
	tool.GenRepo("business", "trading_section", "TradingSection", "trading_section", "ITradingSectionDo", "TradingSection")
	tool.GenRepo("business", "trading_section", "TradingSectionItem", "trading_section_item", "ITradingSectionItemDo", "TradingSectionItem")
	tool.GenRepo("business", "trading_section", "TradingSectionDailyStat", "trading_section_daily_stat", "ITradingSectionDailyStatDo", "TradingSectionDailyStat")
	tool.GenRepo("business", "trading_section", "AnnTradingSection", "ann_trading_section", "IAnnTradingSectionDo", "AnnTradingSection")
	tool.GenRepo("business", "trading_section", "OperAnnTradingSection", "oper_ann_trading_section", "IOperAnnTradingSectionDo", "OperAnnTradingSection")
	tool.GenRepo("business", "trading_section", "MarketChangesTradingSection", "market_changes_trading_section", "IMarketChangesTradingSectionDo", "MarketChangesTradingSection")
}
