package tmt

import (
	"app_service/pkg/util"
	"context"
	"errors"
	"net"
	"time"

	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type GetUserC2cFeeRsp struct {
	Code int32   `json:"code" form:"code"`
	Desc string  `json:"desc" form:"desc"`
	Data float32 `json:"data" form:"data"`
}

// GetUserC2cFee 获取用户手续费
func GetUserC2cFee(ctx context.Context, userId string) (float32, error) {
	rsp := &GetUserC2cFeeRsp{}
	req, err := request.Tmt()
	if err != nil {
		return 0, err
	}

	err = req.Call(
		ctx,
		"open/user/v1/c2c_fee/"+userId,
		&rsp,
		utilRequest.WithMethodGet(),
		utilRequest.WithTimeOut(60*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			return 0, response.Fail.SetMsg("获取用户手续费请求超时")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("获取用户手续费超时异常，返回数据：%v", err)
			return 0, response.Fail.SetMsg("获取用户手续费请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("获取用户手续费异常，返回数据：%v", err)
		return 0, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("获取用户手续费异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return 0, response.Fail.SetMsg("获取用户手续费失败")
	}

	return rsp.Data, err
}
