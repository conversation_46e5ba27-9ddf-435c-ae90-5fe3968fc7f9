package tmt

import (
	"app_service/pkg/util"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"net"
	"time"
)

type GetCustomConfigRsp struct {
	Code int32  `json:"code" form:"code"`
	Desc string `json:"desc" form:"desc"`
	Data any    `json:"data" form:"data"`
}

// GetCustomConfig 获取自定义配置
func GetCustomConfig(ctx context.Context, key string) (any, error) {
	rsp := &GetCustomConfigRsp{}
	req, err := request.Tmt()
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"key": key,
	}

	err = req.Call(
		ctx,
		"open/config/v1/get_custom_config",
		&rsp,
		utilRequest.WithMethodGet(),
		utilRequest.WithParams(params),
		utilRequest.WithTimeOut(60*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			return nil, response.Fail.SetMsg("查询自定义配置请求超时")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("查询自定义配置超时异常，返回数据：%v", err)
			return nil, response.Fail.SetMsg("查询自定义配置请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("查询自定义配置异常，返回数据：%v", err)
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询自定义配置异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg("查询自定义配置失败")
	}
	return rsp.Data, err
}
