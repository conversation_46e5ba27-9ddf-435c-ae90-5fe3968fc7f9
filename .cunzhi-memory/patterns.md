# 常用模式和最佳实践
## 技术实现模式和最佳实践

- 先添加代码再添加导入，防止导入被自动删除
- 会话表设计规则：一条会话记录两条数据，支持已读、删除联系人功能；索引性能考虑用户查询联系人时sql条件为where uid_a=?，联系人表索引为uid_a；消息表一条消息记录一条数据，使用<big_uid, small_uid>联合索引，查询sql为where big_uid=max(uid_a,uid_b) and small_uid=min(uid_a,uid_b)，根据direction字段展示聊天方向
- 消息发送频率限制实现方案：会话表增加is_frequency_limited标记字段，发送消息时判断标记，有标记则查询消息数量判断是否拦截，对方回复时清除标记，避免每次都查询数据库提高性能
- SendMessage事务处理已优化：参考UserExchangeBonusItem的事务模式，使用tx.Create()直接操作而非repo方法；UpdateConversationLastMessage方法也已修改为使用tx.Model()直接操作，并增加了RowsAffected检查确保更新成功，提高了事务的一致性和错误处理能力
