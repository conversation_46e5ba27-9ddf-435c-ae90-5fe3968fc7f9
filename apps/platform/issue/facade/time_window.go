package facade

import (
	"app_service/pkg/util"
	"app_service/third_party/tmt"
	"context"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
)

type TimeWindowEnum int32

const (
	TimeWindowBeforeTrading TimeWindowEnum = iota + 1 // 开市前
	TimeWindowTrading                                 // 开市中
	TimeWindowClose                                   // 已闭市
)

func GetTimeWindow(ctx context.Context) TimeWindowEnum {
	logPrefix := "GetTimeWindow"
	// 获取交易时间配置
	marketTimeConfig, err := tmt.GetCustomConfig(ctx, "market_time")
	if err != nil {
		log.Ctx(ctx).Errorf("%s GetCustomConfig error: %v", logPrefix, err)
	}

	nowTime := util.Now()
	startTime, endTime := GetMarketTimeFromConfig(marketTimeConfig)

	if nowTime.Before(startTime) {
		return TimeWindowBeforeTrading
	} else if nowTime.After(endTime) {
		return TimeWindowClose
	}

	return TimeWindowTrading
}

func GetMarketTimeFromConfig(marketTimeConfig any) (startTime, endTime time.Time) {
	startHourTime, _ := time.Parse("15:04", "09:00")
	endHourTime, _ := time.Parse("15:04", "23:00")
	nowTime := util.Now()
	// 开市时间
	startTime = time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), startHourTime.Hour(), startHourTime.Minute(), 0, 0, nowTime.Location())
	// 闭市时间
	endTime = time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), endHourTime.Hour(), endHourTime.Minute(), 0, 0, nowTime.Location())

	if marketTimeConfig != nil {
		if data, ok := marketTimeConfig.(map[string]interface{}); ok {
			if startStr, ok := data["start"].(string); ok {
				ht, err := time.Parse("15:04", startStr)
				if err == nil {
					startTime = time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), ht.Hour(), ht.Minute(), 0, 0, nowTime.Location())
				}
			}
			if endStr, ok := data["end"].(string); ok {
				ht, err := time.Parse("15:04", endStr)
				if err == nil {
					endTime = time.Date(nowTime.Year(), nowTime.Month(), nowTime.Day(), ht.Hour(), ht.Minute(), 0, 0, nowTime.Location())
				}
			}
		}
	}

	return startTime, endTime
}
