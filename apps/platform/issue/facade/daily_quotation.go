package facade

import (
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/global"
	"app_service/pkg/util"
	"context"
	"sort"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/chenmingyong0423/go-mongox/v2"
	"github.com/chenmingyong0423/go-mongox/v2/builder/aggregation"
	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

// GetPreClosePriceMapByItemIDs 获取商品上个交易日的收盘价
func GetPreClosePriceMapByItemIDs(ctx context.Context, itemIDs []string) (map[string]int32, error) {
	logPrefix := "GetPreClosePriceMapByItemIDs"
	// 先从缓存取
	cacheKeyPrefix := "app_service:item:pre_close_price:"
	result := map[string]int32{}
	var noCacheItemIDs []string
	for _, itemID := range itemIDs {
		preClosePrice, err := global.REDIS.Get(ctx, cacheKeyPrefix+itemID).Int()
		if err == nil {
			result[itemID] = int32(preClosePrice)
		} else {
			noCacheItemIDs = append(noCacheItemIDs, itemID)
		}
	}
	if len(noCacheItemIDs) == 0 {
		return result, nil
	}

	var itemObjectIds []any
	for _, itemId := range noCacheItemIDs {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		itemObjectIds = append(itemObjectIds, itemObjectId)
	}
	dailyQuotationCollection := mongox.NewCollection[mongdb.DailyQuotation](global.Tmt(), "daily_quotation")

	now := time.Now()
	pipeline := aggregation.NewStageBuilder().
		// 阶段1：查询条件
		Match(
			query.NewBuilder().
				In("item_id", itemObjectIds...).
				Lt("date", now.Format("2006-01-02")). // 日期小于今天的最后一个收盘价
				Build(),
		).
		// 阶段2：根据收盘价日期倒序排序
		Sort(bson.M{"date": -1}).
		// 阶段3：按 item_id 分组，取每组第一条记录的日期和收盘价
		Group(
			"$item_id",
			bson.E{
				Key:   "close_price",
				Value: bson.D{bson.E{Key: "$first", Value: "$close_price"}},
			},
			bson.E{
				Key:   "date",
				Value: bson.D{bson.E{Key: "$first", Value: "$date"}},
			},
		).
		// 阶段4：调整输出字段结构
		Project(
			bson.M{
				"_id":         0,
				"date":        1,
				"close_price": 1,
				"item_id":     "$_id", // 将分组 ID 重命名为 item_id
			},
		).
		Build()
	opts := options.Aggregate().SetAllowDiskUse(true)
	lastDailyQuotations, err := dailyQuotationCollection.Aggregator().Pipeline(pipeline).Aggregate(ctx, opts)
	if err != nil {
		return nil, err
	}
	log.Ctx(ctx).Debugf("%s lastDailyQuotations: %v", logPrefix, lastDailyQuotations)
	for _, quotation := range lastDailyQuotations {
		result[quotation.ItemID.Hex()] = quotation.ClosePrice
	}

	// 没有收盘价的商品取首发价作为上一个交易日的收盘价
	var noClosePriceItemIDs []string
	for _, itemID := range noCacheItemIDs {
		if _, exists := result[itemID]; !exists {
			noClosePriceItemIDs = append(noClosePriceItemIDs, itemID)
		}
	}
	if len(noClosePriceItemIDs) > 0 {
		var objIDs []any
		for _, itemId := range noClosePriceItemIDs {
			itemObjectId, err := bson.ObjectIDFromHex(itemId)
			if err != nil {
				return nil, err
			}
			objIDs = append(objIDs, itemObjectId)
		}

		issueItemCollection := mongox.NewCollection[mongdb.IssueItem](global.Tmt(), "issue_item")
		qw := query.NewBuilder().
			In("item_id", objIDs...).
			Build()
		issueItems, err := issueItemCollection.Finder().Filter(qw).Find(ctx)
		if err != nil {
			return nil, err
		}

		log.Ctx(ctx).Debugf("%s issueItems: %v", logPrefix, issueItems)
		for _, issueItem := range issueItems {
			result[issueItem.ItemID.Hex()] = issueItem.Price
		}
	}

	// 缓存
	todayEnd := util.GetEndOfDay(now)
	for itemID, v := range result {
		cacheKey := cacheKeyPrefix + itemID

		_, err = global.REDIS.Set(ctx, cacheKey, v, todayEnd.Sub(now)).Result()
		if err != nil {
			log.Ctx(ctx).Errorf("%s redis set error: %v", logPrefix, err)
		}
	}

	return result, nil
}

// GetLatestClosePriceMapByItemIDs 获取商品最新收盘价
func GetLatestClosePriceMapByItemIDs(ctx context.Context, itemIDs []string) (map[string]int32, error) {
	logPrefix := "GetLatestClosePriceMapByItemIDs"
	var itemObjectIds []any
	for _, itemId := range itemIDs {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		itemObjectIds = append(itemObjectIds, itemObjectId)
	}
	dailyQuotationCollection := mongox.NewCollection[mongdb.DailyQuotation](global.Tmt(), "daily_quotation")
	pipeline := aggregation.NewStageBuilder().
		// 阶段1：查询条件
		Match(
			query.NewBuilder().
				In("item_id", itemObjectIds...).
				Build(),
		).
		// 阶段2：根据收盘价日期倒序排序
		Sort(bson.M{"date": -1}).
		// 阶段3：按 item_id 分组，取每组第一条记录的日期和收盘价
		Group(
			"$item_id",
			bson.E{
				Key:   "close_price",
				Value: bson.D{bson.E{Key: "$first", Value: "$close_price"}},
			},
			bson.E{
				Key:   "date",
				Value: bson.D{bson.E{Key: "$first", Value: "$date"}},
			},
		).
		// 阶段4：调整输出字段结构
		Project(
			bson.M{
				"_id":         0,
				"date":        1,
				"close_price": 1,
				"item_id":     "$_id", // 将分组 ID 重命名为 item_id
			},
		).
		Build()
	opts := options.Aggregate().SetAllowDiskUse(true)
	lastDailyQuotations, err := dailyQuotationCollection.Aggregator().Pipeline(pipeline).Aggregate(ctx, opts)
	if err != nil {
		return nil, err
	}
	log.Ctx(ctx).Debugf("%s lastDailyQuotations: %v", logPrefix, lastDailyQuotations)
	resultMap := map[string]int32{}
	for _, quotation := range lastDailyQuotations {
		resultMap[quotation.ItemID.Hex()] = quotation.ClosePrice
	}

	// 没有收盘价的商品取首发价作为最新收盘价
	var noClosePriceItemIDs []string
	for _, itemID := range itemIDs {
		if _, exists := resultMap[itemID]; !exists {
			noClosePriceItemIDs = append(noClosePriceItemIDs, itemID)
		}
	}
	if len(noClosePriceItemIDs) > 0 {
		var objIDs []any
		for _, itemId := range noClosePriceItemIDs {
			itemObjectId, err := bson.ObjectIDFromHex(itemId)
			if err != nil {
				return nil, err
			}
			objIDs = append(objIDs, itemObjectId)
		}

		issueItemCollection := mongox.NewCollection[mongdb.IssueItem](global.Tmt(), "issue_item")
		qw := query.NewBuilder().
			In("item_id", objIDs...).
			Build()
		issueItems, err := issueItemCollection.Finder().Filter(qw).Find(ctx)
		if err != nil {
			return nil, err
		}

		log.Ctx(ctx).Debugf("%s issueItems: %v", logPrefix, issueItems)
		for _, issueItem := range issueItems {
			resultMap[issueItem.ItemID.Hex()] = issueItem.Price
		}
	}

	return resultMap, nil
}

// GetLatestClosePriceRecords 获取单个商品最新收盘价记录
func GetLatestClosePriceRecords(ctx context.Context, itemID string, limit int64) ([]*mongdb.DailyQuotation, error) {
	dailyQuotationCollection := mongox.NewCollection[mongdb.DailyQuotation](global.Tmt(), "daily_quotation")
	itemObjectId, _ := bson.ObjectIDFromHex(itemID)
	qb := query.NewBuilder().
		Eq("item_id", itemObjectId).
		Build()
	return dailyQuotationCollection.Finder().Filter(qb).Sort(bson.M{"date": -1}).Limit(limit).Find(ctx)
}

// GetPreTradingDate 获取上个交易日的日期
func GetPreTradingDate(ctx context.Context) (string, error) {
	dailyQuotationCollection := mongox.NewCollection[mongdb.DailyQuotation](global.Tmt(), "daily_quotation")
	qb := query.NewBuilder().
		Lt("date", util.Now().Format("2006-01-02")).
		Build()
	records, err := dailyQuotationCollection.Finder().Filter(qb).Sort(bson.M{"date": -1}).Limit(1).Find(ctx)
	if err != nil {
		return "", err
	}
	dateStr := ""
	if len(records) > 0 {
		dateStr = records[0].Date
	}

	return dateStr, nil
}

// GetAllClosePriceMap 获取多个商品所有收盘价记录 Map
func GetAllClosePriceMap(ctx context.Context, itemIDs []string) (map[string][]*mongdb.DailyQuotation, error) {
	dailyQuotationCollection := mongox.NewCollection[mongdb.DailyQuotation](global.Tmt(), "daily_quotation")
	var objItemIDs []any
	for _, itemID := range itemIDs {
		itemObjectId, err := bson.ObjectIDFromHex(itemID)
		if err != nil {
			return nil, err
		}
		objItemIDs = append(objItemIDs, itemObjectId)
	}
	qb := query.NewBuilder().
		In("item_id", objItemIDs...).
		Build()
	records, err := dailyQuotationCollection.Finder().Filter(qb).Find(ctx)
	if err != nil {
		return nil, err
	}
	resultMap := map[string][]*mongdb.DailyQuotation{}
	for _, record := range records {
		if _, ok := resultMap[record.ItemID.Hex()]; !ok {
			resultMap[record.ItemID.Hex()] = []*mongdb.DailyQuotation{record}
		} else {
			resultMap[record.ItemID.Hex()] = append(resultMap[record.ItemID.Hex()], record)
		}
	}

	// 按照交易日从小到大排序
	for itemID, list := range resultMap {
		sort.Slice(list, func(i, j int) bool {
			return list[i].Date < list[j].Date
		})
		resultMap[itemID] = list
	}

	return resultMap, nil
}
