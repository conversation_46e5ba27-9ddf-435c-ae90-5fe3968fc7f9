package facade

import (
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/global"
	"app_service/pkg/util"
	"context"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/chenmingyong0423/go-mongox/v2"
	"github.com/chenmingyong0423/go-mongox/v2/builder/aggregation"
	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

func GetLatestItemWithdrawOrderByIDs(ctx context.Context, ids []string) (*mongdb.ItemWithdrawOrder, error) {
	iwoCollection := mongox.NewCollection[mongdb.ItemWithdrawOrder](global.Tmt(), "item_withdraw_orders")
	var objIDs []any
	for _, id := range ids {
		objectId, err := bson.ObjectIDFromHex(id)
		if err != nil {
			return nil, err
		}
		objIDs = append(objIDs, objectId)
	}

	qw := query.NewBuilder().
		In("_id", objIDs...).
		Build()
	return iwoCollection.Finder().Filter(qw).Sort(bson.M{"created_at": -1}).FindOne(ctx)
}

// GetTransactionQtyMapByItemIDs [二手]获取 item_id 成交数量 map
func GetTransactionQtyMapByItemIDs(ctx context.Context, startTime, endTime time.Time, itemIDs []string) (map[string]int64, error) {
	var itemObjectIds []any
	for _, itemId := range itemIDs {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		itemObjectIds = append(itemObjectIds, itemObjectId)
	}
	type aggregationItem struct {
		ItemID         bson.ObjectID `bson:"item_id"`
		TransactionQty int64         `bson:"transaction_qty"`
	}
	iwoCollection := mongox.NewCollection[aggregationItem](global.Tmt(), "item_withdraw_orders")
	pipeline := aggregation.NewStageBuilder().
		Match(
			query.NewBuilder().
				In("extends.items.item_id", itemObjectIds...).
				Eq("status", mongdb.ItemWithdrawOrderStatusDone).
				Gte("created_at", startTime).
				Lte("created_at", endTime).
				Build(),
		).
		Unwind("$extends.items", &aggregation.UnWindOptions{}).
		Match(
			query.NewBuilder().In("extends.items.item_id", itemObjectIds...).Build(),
		).
		Group("$extends.items.item_id",
			bson.E{
				Key:   "transaction_qty",
				Value: bson.D{bson.E{Key: "$sum", Value: "$extends.items.quantity"}}},
		).
		Project(
			bson.M{
				"_id":             0,
				"transaction_qty": 1,
				"item_id":         "$_id",
			},
		).Build()
	opts := options.Aggregate().SetAllowDiskUse(true)
	results, err := iwoCollection.Aggregator().Pipeline(pipeline).Aggregate(ctx, opts)
	if err != nil {
		return nil, err
	}
	resultMap := map[string]int64{}
	for _, result := range results {
		resultMap[result.ItemID.Hex()] = result.TransactionQty
	}

	return resultMap, nil
}

// GetTotalTransactionAmountMapByItemIDs [二手]根据 item_id 获取历史累计交额 map
func GetTotalTransactionAmountMapByItemIDs(ctx context.Context, itemIDs []string) (map[string]int64, error) {
	var itemObjectIds []any
	for _, itemId := range itemIDs {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		itemObjectIds = append(itemObjectIds, itemObjectId)
	}
	type aggregationItem struct {
		ItemID            bson.ObjectID `bson:"item_id"`
		TransactionAmount int64         `bson:"transaction_amount"`
	}
	iwoCollection := mongox.NewCollection[aggregationItem](global.Tmt(), "item_withdraw_orders")
	pipeline := aggregation.NewStageBuilder().
		Match(
			query.NewBuilder().
				In("extends.items.item_id", itemObjectIds...).
				Eq("status", mongdb.ItemWithdrawOrderStatusDone).
				Build(),
		).
		Unwind("$extends.items", &aggregation.UnWindOptions{}).
		Match(
			query.NewBuilder().In("extends.items.item_id", itemObjectIds...).Build(),
		).
		Group("$extends.items.item_id",
			bson.E{
				Key:   "transaction_amount",
				Value: bson.D{bson.E{Key: "$sum", Value: "$extends.items.pay_amount"}},
			},
		).
		Project(
			bson.M{
				"_id":                0,
				"transaction_amount": 1,
				"item_id":            "$_id",
			},
		).Build()
	opts := options.Aggregate().SetAllowDiskUse(true)
	results, err := iwoCollection.Aggregator().Pipeline(pipeline).Aggregate(ctx, opts)
	if err != nil {
		return nil, err
	}
	resultMap := map[string]int64{}
	for _, result := range results {
		resultMap[result.ItemID.Hex()] = result.TransactionAmount
	}

	return resultMap, nil
}

// GetTransactionAmountMapByTime [二手]根据时间范围获取成交额 map
func GetTransactionAmountMapByTime(ctx context.Context, startTime, endTime time.Time) (map[string]int64, error) {
	type aggregationItem struct {
		ItemID            bson.ObjectID `bson:"item_id"`
		TransactionAmount int64         `bson:"transaction_amount"`
	}
	iwoCollection := mongox.NewCollection[aggregationItem](global.Tmt(), "item_withdraw_orders")
	pipeline := aggregation.NewStageBuilder().
		Match(
			query.NewBuilder().
				Eq("status", mongdb.ItemWithdrawOrderStatusDone).
				Gte("created_at", startTime).
				Lte("created_at", endTime).
				Build(),
		).
		Unwind("$extends.items", &aggregation.UnWindOptions{}).
		Group("$extends.items.item_id",
			bson.E{
				Key:   "transaction_amount",
				Value: bson.D{bson.E{Key: "$sum", Value: "$extends.items.pay_amount"}}},
		).
		Project(
			bson.M{
				"_id":                0,
				"transaction_amount": 1,
				"item_id":            "$_id",
			},
		).Build()
	opts := options.Aggregate().SetAllowDiskUse(true)
	results, err := iwoCollection.Aggregator().Pipeline(pipeline).Aggregate(ctx, opts)
	if err != nil {
		return nil, err
	}
	resultMap := map[string]int64{}
	for _, result := range results {
		resultMap[result.ItemID.Hex()] = result.TransactionAmount
	}

	return resultMap, nil
}

// GetTransactionAmountMapByTimeAndItem [二手]根据时间范围和指定商品获取成交额 map
func GetTransactionAmountMapByTimeAndItem(ctx context.Context, startTime, endTime time.Time, itemIDs []string) (map[string]int64, error) {
	var itemObjectIds []any
	for _, itemId := range itemIDs {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		itemObjectIds = append(itemObjectIds, itemObjectId)
	}
	type aggregationItem struct {
		ItemID            bson.ObjectID `bson:"item_id"`
		TransactionAmount int64         `bson:"transaction_amount"`
	}
	iwoCollection := mongox.NewCollection[aggregationItem](global.Tmt(), "item_withdraw_orders")
	pipeline := aggregation.NewStageBuilder().
		Match(
			query.NewBuilder().
				Eq("status", mongdb.ItemWithdrawOrderStatusDone).
				In("extends.items.item_id", itemObjectIds...).
				Gte("created_at", startTime).
				Lte("created_at", endTime).
				Build(),
		).
		Unwind("$extends.items", &aggregation.UnWindOptions{}).
		Group("$extends.items.item_id",
			bson.E{
				Key:   "transaction_amount",
				Value: bson.D{bson.E{Key: "$sum", Value: "$extends.items.pay_amount"}}},
		).
		Project(
			bson.M{
				"_id":                0,
				"transaction_amount": 1,
				"item_id":            "$_id",
			},
		).Build()
	opts := options.Aggregate().SetAllowDiskUse(true)
	results, err := iwoCollection.Aggregator().Pipeline(pipeline).Aggregate(ctx, opts)
	if err != nil {
		return nil, err
	}
	resultMap := map[string]int64{}
	for _, result := range results {
		resultMap[result.ItemID.Hex()] = result.TransactionAmount
	}

	return resultMap, nil
}

// GetPreTransactionDateMap 获取二手商品上一个最近交易日期 map
func GetPreTransactionDateMap(ctx context.Context, itemIDs []string) (map[string]time.Time, error) {
	var itemObjectIds []any
	for _, itemId := range itemIDs {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		itemObjectIds = append(itemObjectIds, itemObjectId)
	}
	type aggregationItem struct {
		ItemID    bson.ObjectID `bson:"item_id"`
		CreatedAt time.Time     `bson:"created_at"`
	}
	nowTime := util.Now()
	todayBegin := util.GetStartOfDay(nowTime)
	iwoCollection := mongox.NewCollection[aggregationItem](global.Tmt(), "item_withdraw_orders")
	pipeline := aggregation.NewStageBuilder().
		Match(
			query.NewBuilder().
				In("extends.items.item_id", itemObjectIds...).
				Eq("status", mongdb.ItemWithdrawOrderStatusDone).
				Lt("created_at", todayBegin).
				Build(),
		).
		Sort(bson.M{"created_at": -1}).
		Unwind("$extends.items", &aggregation.UnWindOptions{}).
		Match(
			query.NewBuilder().In("extends.items.item_id", itemObjectIds...).Build(),
		).
		Group("$extends.items.item_id",
			bson.E{
				Key:   "created_at",
				Value: bson.D{bson.E{Key: "$first", Value: "$created_at"}},
			},
		).
		Project(
			bson.M{
				"_id":        0,
				"created_at": 1,
				"item_id":    "$_id",
			},
		).Build()
	opts := options.Aggregate().SetAllowDiskUse(true)
	results, err := iwoCollection.Aggregator().Pipeline(pipeline).Aggregate(ctx, opts)
	if err != nil {
		return nil, err
	}
	resultMap := map[string]time.Time{}
	for _, result := range results {
		resultMap[result.ItemID.Hex()] = result.CreatedAt
	}

	return resultMap, nil
}

// GetLatestSellPriceMapByItemIDs 获取商品最新成交价 map （使用首发价格兜底）
func GetLatestSellPriceMapByItemIDs(ctx context.Context, itemIDs []string) (map[string]int32, error) {
	var itemObjectIds []any
	for _, itemId := range itemIDs {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		itemObjectIds = append(itemObjectIds, itemObjectId)
	}
	type aggregationItem struct {
		ItemID    bson.ObjectID `bson:"item_id"`
		LastPrice int32         `bson:"last_price"`
	}
	itemWithdrawOrderCollection := mongox.NewCollection[aggregationItem](global.Tmt(), "item_withdraw_orders")
	pipeline := aggregation.NewStageBuilder().
		Match(
			query.NewBuilder().
				In("extends.items.item_id", itemObjectIds...).
				Eq("status", mongdb.ItemWithdrawOrderStatusDone).
				Build(),
		).
		Unwind("$extends.items", &aggregation.UnWindOptions{}).
		Sort(bson.M{"created_at": -1}).
		Group("$extends.items.item_id",
			bson.E{
				Key:   "last_price",
				Value: bson.D{bson.E{Key: "$first", Value: "$extends.items.sell_price"}}},
		).
		Project(
			bson.M{
				"_id":        0,
				"last_price": 1,
				"item_id":    "$_id",
			},
		).
		Build()
	opts := options.Aggregate().SetAllowDiskUse(true)
	orderResults, err := itemWithdrawOrderCollection.Aggregator().Pipeline(pipeline).Aggregate(ctx, opts)
	if err != nil {
		return nil, err
	}
	resultMap := make(map[string]int32, len(itemIDs))
	var saleOrderItemIDs []string
	for _, orderResult := range orderResults {
		saleOrderItemIDs = append(saleOrderItemIDs, orderResult.ItemID.Hex())
		resultMap[orderResult.ItemID.Hex()] = orderResult.LastPrice
	}
	// 没有订单的商品 id
	var noSaleOrderItemIDs []string
	for _, itemID := range itemIDs {
		if _, exists := resultMap[itemID]; !exists {
			noSaleOrderItemIDs = append(noSaleOrderItemIDs, itemID)
		}
	}
	if len(noSaleOrderItemIDs) > 0 {
		issueItemMap, err := GetIssueItemMap(ctx, noSaleOrderItemIDs)
		if err != nil {
			log.Ctx(ctx).Errorf("GetIssueItemMap error: %v", err)
			return nil, err
		}

		for _, issueItem := range issueItemMap {
			resultMap[issueItem.ItemID.Hex()] = issueItem.Price
		}
	}

	return resultMap, nil
}

// GetTodayLatestSellPriceMapByItemIDs 获取商品当天最新成交价 map
func GetTodayLatestSellPriceMapByItemIDs(ctx context.Context, itemIDs []string) (map[string]int32, error) {
	var itemObjectIds []any
	for _, itemId := range itemIDs {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		itemObjectIds = append(itemObjectIds, itemObjectId)
	}
	type aggregationItem struct {
		ItemID    bson.ObjectID `bson:"item_id"`
		LastPrice int32         `bson:"last_price"`
	}
	itemWithdrawOrderCollection := mongox.NewCollection[aggregationItem](global.Tmt(), "item_withdraw_orders")
	nowTime := util.Now()
	startTime := util.GetStartOfDay(nowTime)
	endTime := util.GetEndOfDay(nowTime)
	pipeline := aggregation.NewStageBuilder().
		Match(
			query.NewBuilder().
				In("extends.items.item_id", itemObjectIds...).
				Eq("status", mongdb.ItemWithdrawOrderStatusDone).
				Gte("created_at", startTime).
				Lte("created_at", endTime).
				Build(),
		).
		Unwind("$extends.items", &aggregation.UnWindOptions{}).
		Sort(bson.M{"created_at": -1}).
		Group("$extends.items.item_id",
			bson.E{
				Key:   "last_price",
				Value: bson.D{bson.E{Key: "$first", Value: "$extends.items.sell_price"}}},
		).
		Project(
			bson.M{
				"_id":        0,
				"last_price": 1,
				"item_id":    "$_id",
			},
		).
		Build()
	opts := options.Aggregate().SetAllowDiskUse(true)
	orderResults, err := itemWithdrawOrderCollection.Aggregator().Pipeline(pipeline).Aggregate(ctx, opts)
	if err != nil {
		return nil, err
	}
	resultMap := make(map[string]int32, len(itemIDs))
	var saleOrderItemIDs []string
	for _, orderResult := range orderResults {
		saleOrderItemIDs = append(saleOrderItemIDs, orderResult.ItemID.Hex())
		resultMap[orderResult.ItemID.Hex()] = orderResult.LastPrice
	}

	return resultMap, nil
}

// GetLatestSellPriceMapBeforeTime 获取商品某个时间点之前的最新成交价 map （使用首发价格兜底）
func GetLatestSellPriceMapBeforeTime(ctx context.Context, itemIDs []string, beforeTime time.Time) (map[string]int32, error) {
	var itemObjectIds []any
	for _, itemId := range itemIDs {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		itemObjectIds = append(itemObjectIds, itemObjectId)
	}
	type aggregationItem struct {
		ItemID    bson.ObjectID `bson:"item_id"`
		LastPrice int32         `bson:"last_price"`
	}
	itemWithdrawOrderCollection := mongox.NewCollection[aggregationItem](global.Tmt(), "item_withdraw_orders")
	pipeline := aggregation.NewStageBuilder().
		Match(
			query.NewBuilder().
				In("extends.items.item_id", itemObjectIds...).
				Eq("status", mongdb.ItemWithdrawOrderStatusDone).
				Lte("created_at", beforeTime).
				Build(),
		).
		Unwind("$extends.items", &aggregation.UnWindOptions{}).
		Sort(bson.M{"created_at": -1}).
		Group("$extends.items.item_id",
			bson.E{
				Key:   "last_price",
				Value: bson.D{bson.E{Key: "$first", Value: "$extends.items.sell_price"}}},
		).
		Project(
			bson.M{
				"_id":        0,
				"last_price": 1,
				"item_id":    "$_id",
			},
		).
		Build()
	opts := options.Aggregate().SetAllowDiskUse(true)
	orderResults, err := itemWithdrawOrderCollection.Aggregator().Pipeline(pipeline).Aggregate(ctx, opts)
	if err != nil {
		return nil, err
	}
	resultMap := make(map[string]int32, len(itemIDs))
	var saleOrderItemIDs []string
	for _, orderResult := range orderResults {
		saleOrderItemIDs = append(saleOrderItemIDs, orderResult.ItemID.Hex())
		resultMap[orderResult.ItemID.Hex()] = orderResult.LastPrice
	}

	// 没有订单的商品 id
	var noSaleOrderItemIDs []string
	for _, itemID := range itemIDs {
		if _, exists := resultMap[itemID]; !exists {
			noSaleOrderItemIDs = append(noSaleOrderItemIDs, itemID)
		}
	}
	if len(noSaleOrderItemIDs) > 0 {
		issueItemMap, err := GetIssueItemMap(ctx, noSaleOrderItemIDs)
		if err != nil {
			log.Ctx(ctx).Errorf("GetIssueItemMap error: %v", err)
			return nil, err
		}

		for _, issueItem := range issueItemMap {
			resultMap[issueItem.ItemID.Hex()] = issueItem.Price
		}
	}

	return resultMap, nil
}
