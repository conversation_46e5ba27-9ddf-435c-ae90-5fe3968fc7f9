package service

import (
	"app_service/apps/business/notification/dal/model"
	"app_service/apps/business/notification/define"
	"app_service/apps/business/notification/define/enums"
	"app_service/apps/business/notification/facade"
	"app_service/apps/business/notification/repo"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"errors"
	"gorm.io/gorm"
	"time"
)

func (s *Service) RegisterDevice(req *define.RegisterDeviceReq) (*define.RegisterDeviceResp, error) {
	userInfo := s.GetUserFromCtx()
	if userInfo == nil {
		return nil, errors.New("获取用户信息失败")
	}
	channelID := s.ctx.Value(middlewares.AppChannel).(string)
	if channelID == "" {
		return nil, define.NT310002Err
	}
	deviceSchema := repo.GetQuery().PushDevice
	// 设备唯一标识可能会变，极光注册 id 同一个 app key 在同一个设备上不会变
	qb := search.NewQueryBuilder().Eq(deviceSchema.DeviceID, req.DeviceID).Or().Eq(deviceSchema.PushID, req.PushID).
		Done().Build()
	device, err := repo.NewPushDeviceRepo(deviceSchema.WithContext(s.ctx)).SelectOne(qb)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	now := util.Now()
	var createdDevice *model.PushDevice
	if device == nil {
		newDevice := &model.PushDevice{
			PushDeviceID: snowflakeutl.GenerateID(),
			DeviceID:     req.DeviceID,
			Platform:     req.Platform,
			Provider:     req.Provider,
			PushID:       req.PushID,
			UserID:       userInfo.Id,
			ChannelID:    channelID,
			DeviceModel:  req.DeviceModel,
			OsVersion:    req.OSVersion,
			Status:       enums.PushDeviceStatusOnline.Val(),
			LastActive:   &now,
		}
		err = repo.NewPushDeviceRepo(deviceSchema.WithContext(s.ctx)).Save(newDevice)
		if err != nil {
			return nil, err
		}
		createdDevice = newDevice
	} else {
		device.DeviceID = req.DeviceID
		device.PushID = req.PushID
		device.Platform = req.Platform
		device.Provider = req.Provider
		device.UserID = userInfo.Id
		device.ChannelID = channelID
		device.DeviceModel = req.DeviceModel
		device.OsVersion = req.OSVersion
		device.Status = enums.PushDeviceStatusOnline.Val()
		device.LastActive = &now
		err = repo.NewPushDeviceRepo(deviceSchema.WithContext(s.ctx)).UpdateById(device)
		if err != nil {
			return nil, err
		}
		createdDevice = device
	}

	return &define.RegisterDeviceResp{
		ID:        createdDevice.PushDeviceID,
		CreatedAt: util.Now().Format(time.RFC3339),
	}, nil
}

func (s *Service) UpdateDeviceStatus(req *define.UpdateDeviceStatusReq) (*define.UpdateDeviceStatusResp, error) {
	deviceSchema := repo.GetQuery().PushDevice
	qb := search.NewQueryBuilder().Eq(deviceSchema.DeviceID, req.DeviceID).Build()
	device, err := repo.NewPushDeviceRepo(deviceSchema.WithContext(s.ctx)).SelectOne(qb)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, define.NT310001Err
		}
		return nil, err
	}
	device.Status = req.Status
	err = repo.NewPushDeviceRepo(deviceSchema.WithContext(s.ctx)).UpdateById(device)
	if err != nil {
		return nil, err
	}

	return &define.UpdateDeviceStatusResp{
		ID:        device.PushDeviceID,
		UpdatedAt: util.Now().Format(time.RFC3339),
	}, nil
}

func (s *Service) TestPush(req *define.TestPushReq) (*define.TestPushResp, error) {
	if global.GlobalConfig.Service.Env == global.EnvProd {
		return nil, errors.New("该接口不能用于生产环境")
	}

	msg := define.PushMessage{
		Title:          req.Title,
		Content:        req.Content,
		AudienceType:   enums.PushAudienceTypePushID,
		PushIDs:        req.PushIDs,
		AndroidExtras:  req.Extras,
		IOSExtras:      req.Extras,
		ApnsProduction: req.ApnsProduction,
	}
	relateInfo := define.PushRelateInfo{
		RelateType:  enums.PushRelateTypeTest,
		RelateScene: enums.PushRelateSceneTest,
		RelateID:    util.StrVal(snowflakeutl.GenerateID()),
	}

	result, err := facade.PushMessage(s.ctx, msg, relateInfo)
	if err != nil {
		return nil, err
	}
	return &define.TestPushResp{
		MessageID: result.MessageID,
	}, nil
}
