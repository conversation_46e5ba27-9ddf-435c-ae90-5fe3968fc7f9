package define

import (
	"time"

	"app_service/apps/business/card_community/define/enums"
	"app_service/pkg/pagination"
)

// 会话列表相关结构体
type (
	// GetConversationListReq 获取会话列表请求
	GetConversationListReq struct {
		pagination.Pagination
	}

	// GetConversationListData 会话列表数据
	GetConversationListData struct {
		ID                 string            `json:"id"`                   // 会话记录ID
		OtherParticipant   UserInfo          `json:"other_participant"`    // 对方信息
		LastMessageContent string            `json:"last_message_content"` // 最后消息内容
		LastMessageTime    *time.Time        `json:"last_message_time"`    // 最后消息时间
		LastMessageType    enums.MessageType `json:"last_message_type"`    // 最后消息类型：1=文本 2=图片 3=帖子快照 4=订单
		UnreadCount        int               `json:"unread_count"`         // 未读消息数量
		CreatedAt          time.Time         `json:"created_at"`           // 创建时间
	}

	// GetConversationListResp 获取会话列表响应
	GetConversationListResp struct {
		List             []*GetConversationListData `json:"list"`               // 会话列表
		HasMore          bool                       `json:"has_more"`           // 是否有更多
		TotalUnreadCount int                        `json:"total_unread_count"` // 未读消息数量
	}
)

// 会话详情相关结构体
type (
	// GetConversationDetailReq 获取会话详情请求
	GetConversationDetailReq struct {
		ID string `form:"id" json:"id" binding:"required"` // 会话记录ID
	}

	// GetConversationDetailResp 获取会话详情响应
	GetConversationDetailResp struct {
		ID               string                `json:"id"`                // 会话记录ID
		ParticipantType  enums.ParticipantType `json:"participant_type"`  // 参与者类型：1=用户 2=商家
		OtherParticipant UserInfo              `json:"other_participant"` // 对方信息
		PostID           string                `json:"post_id"`           // 关联的帖子ID
		Status           int32                 `json:"status"`            // 状态：1=正常 -1=限制中
		CreatedAt        time.Time             `json:"created_at"`        // 创建时间
	}
)

// 创建会话相关结构体
type (
	// CreateConversationReq 创建会话请求
	CreateConversationReq struct {
		OtherParticipantID string `json:"other_participant_id" binding:"required"` // 对方ID（用户ID或商家ID）
	}

	// CreateConversationResp 创建会话响应
	CreateConversationResp struct {
		ID    string `json:"id"`     // 会话记录ID
		IsNew bool   `json:"is_new"` // 是否新创建
	}
)
