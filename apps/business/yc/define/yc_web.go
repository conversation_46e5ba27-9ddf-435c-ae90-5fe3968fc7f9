package define

import (
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/pkg/pagination"
	"time"
)

type FlowStatusEnum int32

// FlowStatus 流转状态枚举
const (
	FlowStatusAvailable FlowStatusEnum = 1 // 可流转
	FlowStatusForbidden FlowStatusEnum = 2 // 禁止流转
	FlowStatusFused     FlowStatusEnum = 4 // 已融合
	FlowStatusCountdown FlowStatusEnum = 5 // 可售倒计时
	FlowStatusStory     FlowStatusEnum = 6 // 已探索

	FlowStatusOrderSell    FlowStatusEnum = 3001 // 出售中
	FlowStatusOrderClose   FlowStatusEnum = 3002 // 已取消
	FlowStatusOrderSellOut FlowStatusEnum = 3003 // 已出售
	FlowStatusOrderOff     FlowStatusEnum = 3004 // 已下架
	FlowStatusOrderSelling FlowStatusEnum = 3005 // 交易中
)

type (
	Item struct {
		ItemID               string                              `json:"item_id"`
		ItemName             string                              `json:"item_name"`
		IconURL              string                              `json:"icon_url"`
		HoldQuantity         int32                               `json:"hold_quantity"`
		TotalCost            int64                               `json:"total_cost"`
		LastMarketPrice      int32                               `json:"last_market_price"`
		MarketValue          int32                               `json:"market_value"`
		ProfitLossValue      int64                               `json:"profit_loss_value"`
		ProfitLostPercentage float64                             `json:"profit_lost_percentage"`
		DeliveryTime         *time.Time                          `json:"delivery_time"`
		SynthesisStatus      mongdb.IssueItemSynthesisStatusEnum `json:"synthesis_status"`
		StoryStatus          mongdb.IssueItemStoryStatusEnum     `json:"story_status"`
	}

	GetWebAllItemsReq struct {
	}
	GetWebAllItemsResp struct {
		Items []Item `json:"items"`
	}

	GetWebStatisticOverviewReq  struct{}
	GetWebStatisticOverviewResp struct {
		HoldQuantity              int32   `json:"hold_quantity"`                // 持仓数量
		TotalCost                 int64   `json:"total_cost"`                   // 持仓成本
		TotalMarketValue          int64   `json:"total_market_value"`           // 持仓市值
		TotalProfitLossValue      int64   `json:"total_profit_loss_value"`      // 持仓盈亏
		TodayProfitLossValue      int64   `json:"today_profit_loss_value"`      // 今日盈亏
		TodayProfitLossPercentage float64 `json:"today_profit_loss_percentage"` // 今日盈亏百分比
	}

	GetWebUserItemListReq struct {
		pagination.Pagination
		Sort string `form:"sort" json:"sort" binding:"required"`
	}
	UserItem struct {
		ID                string         `json:"_id" bson:"_id,omitempty"`      // 文档ID（MongoDB建议使用primitive.ObjectID）
		ItemID            string         `json:"item_id"`                       // 物品唯一标识
		BuyPrice          int32          `json:"buy_price"`                     // 购买价格（单位：分）
		BuyTime           time.Time      `json:"buy_time"`                      // 购买时间（RFC3339格式）
		ReceiveType       int32          `json:"receive_type"`                  // 获得方式（枚举值）
		CreatedAt         time.Time      `json:"created_at"`                    // 记录创建时间
		FlowStatus        FlowStatusEnum `json:"flow_status"`                   // 流转状态
		DaysUntilSellable int32          `json:"days_until_sellable,omitempty"` // 可售倒计时（flow_status=5时生效）
		StoryStatus       int32          `json:"story_status"`                  // 故事玩法派遣状态【0:未探索;1:已探索】
		FusionStatus      int32          `json:"fusion_status"`                 // 融合状态【0:未融合;1:已融合】
	}
	GetWebUserItemListResp struct {
		Tip          float32     `json:"tip"`
		UserItemList []*UserItem `json:"user_item_list"`
	}
)
