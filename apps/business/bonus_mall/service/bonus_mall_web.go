package service

import (
	"app_service/apps/business/bonus_mall/dal/model"
	"app_service/apps/business/bonus_mall/define"
	"app_service/apps/business/bonus_mall/define/enums"
	"app_service/apps/business/bonus_mall/repo"
	"app_service/apps/business/bonus_mall/service/locker"
	"app_service/apps/business/bonus_mall/service/logic"
	assetmodel "app_service/apps/platform/asset/dal/model"
	assetenum "app_service/apps/platform/asset/define/enums"
	issueFacade "app_service/apps/platform/issue/facade"
	userFacade "app_service/apps/platform/user/facade"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"app_service/third_party/tmt"
	"app_service/third_party/yc_open"
	ycopendefine "app_service/third_party/yc_open/define"
	"context"
	"encoding/json"
	"errors"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

// GetExchangeLogWebTopList 获取最新兑换记录列表
func (s *Service) GetExchangeLogWebTopList(req *define.GetExchangeLogWebTopListReq) (*define.GetExchangeLogWebTopListResp, error) {
	exLogSchema := repo.GetQuery().BonusItemExchangeLog
	qb := search.NewQueryBuilder().
		Eq(exLogSchema.Status, enums.ExchangeLogStatusSuccess.Val()).
		OrderByDesc(exLogSchema.ExchangeTime).
		Build()
	exchangeLogList, err := repo.NewBonusItemExchangeLogRepo(exLogSchema.WithContext(s.ctx).Limit(20)).SelectList(qb)
	if err != nil {
		return nil, err
	}

	topList := make([]*define.GetExchangeLogWebTopListItem, 0)
	for _, item := range exchangeLogList {
		topList = append(topList, &define.GetExchangeLogWebTopListItem{
			NickName: util.MaskNickname(item.Nickname, 2),
			Avatar:   item.UserAvatar,
			ItemName: item.ItemName,
		})
	}
	resp := &define.GetExchangeLogWebTopListResp{
		List: topList,
	}

	return resp, nil
}

// GetUserExchangeLogWebList 获取用户兑换记录列表
func (s *Service) GetUserExchangeLogWebList(req *define.GetUserExchangeLogWebListReq) (*define.GetUserExchangeLogWebListResp, error) {
	userID := s.GetUserId()
	if userID == "" {
		return nil, errors.New("获取用户信息失败")
	}

	exLogSchema := repo.GetQuery().BonusItemExchangeLog
	qb := search.NewQueryBuilder().
		Eq(exLogSchema.UserID, userID).
		OrderByDesc(exLogSchema.ExchangeTime).
		Build()
	exchangeLogList, total, err := repo.NewBonusItemExchangeLogRepo(exLogSchema.WithContext(s.ctx)).
		SelectPage(qb, req.GetPage(), req.GetPageSize())
	if err != nil {
		return nil, err
	}
	infoList := make([]*define.GetUserExchangeLogWebListInfo, 0)
	for _, item := range exchangeLogList {
		info := &define.GetUserExchangeLogWebListInfo{
			ID:           item.BonusItemExchangeLogID,
			ItemName:     item.ItemName,
			IconURL:      item.IconURL,
			ExchangeTime: item.ExchangeTime,
			ExchangeQty:  item.ExchangeQty,
			BonusTotal:   item.BonusTotal,
			Status:       item.Status,
			FailedReason: item.FailedReason,
		}
		infoList = append(infoList, info)
	}

	resp := &define.GetUserExchangeLogWebListResp{
		List:  infoList,
		Total: total,
	}
	return resp, nil
}

// GetBonusItemWebList 获取积分商品用户端列表
func (s *Service) GetBonusItemWebList(req *define.GetBonusItemWebListReq) (*define.GetBonusItemWebListResp, error) {
	bonusItemSchema := repo.GetQuery().BonusItem
	var list []*model.BonusItem
	var total int64
	qb := search.NewQueryBuilder().
		Eq(bonusItemSchema.Status, enums.BonusItemStatusAvailable.Val()).
		OrderByDesc(bonusItemSchema.Priority).
		OrderByDesc(bonusItemSchema.CreatedAt).
		Build()
	availableList, availableTotal, err := repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).
		SelectPage(qb, req.GetPage(), req.GetPageSize())

	list = availableList
	total = availableTotal
	// 如果当前没有上架的商品，展示最后 10 个已结束的商品
	if total == 0 {
		expiredCount := 10
		expiredQb := search.NewQueryBuilder().
			Eq(bonusItemSchema.Status, enums.BonusItemStatusExpired.Val()).
			OrderByDesc(bonusItemSchema.Priority).
			OrderByDesc(bonusItemSchema.ExchangeEndTime).
			Build()

		expiredList, expiredTotal, err := repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).
			SelectPage(expiredQb, 1, expiredCount)
		if err != nil {
			return nil, err
		}
		if req.GetPage() == 1 {
			list = expiredList
		}
		total = expiredTotal
	}

	// 获取当前分页的实物商品
	var steamItemIds []string
	for _, v := range list {
		steamItemIds = append(steamItemIds, v.SteamItemID)
	}
	steamItemMap, err := issueFacade.GetSteamItemMap(s.ctx, steamItemIds)
	if err != nil {
		return nil, err
	}

	// 组装数据
	itemInfoList := make([]*define.GetBonusItemWebListInfo, 0)
	for _, bonusItem := range list {
		steamItem, exists := steamItemMap[bonusItem.SteamItemID]
		if !exists {
			return nil, define.BM300003Err
		}
		info := &define.GetBonusItemWebListInfo{
			ID:                bonusItem.BonusItemID,
			StockQty:          bonusItem.StockQty,
			ExchangePrice:     bonusItem.ExchangePrice,
			ExchangeStartTime: bonusItem.ExchangeStartTime,
			ExchangeEndTime:   bonusItem.ExchangeEndTime,
			ItemName:          steamItem.ItemName,
			IconURL:           steamItem.IconURL,
			SellPrice:         steamItem.MarketPrices.Unx.SellPrice,
			CurrentTime:       util.Now().Format(time.RFC3339),
			Status:            bonusItem.Status,
		}
		itemInfoList = append(itemInfoList, info)
	}

	resp := &define.GetBonusItemWebListResp{
		List:  itemInfoList,
		Total: total,
	}
	return resp, nil
}

// GetBonusItemWebDetail 获取积分商品用户端详情
func (s *Service) GetBonusItemWebDetail(req *define.GetBonusItemWebDetailReq) (*define.GetBonusItemWebDetailResp, error) {
	bonusItemSchema := repo.GetQuery().BonusItem
	currentBonusItemQb := search.NewQueryBuilder().Eq(bonusItemSchema.BonusItemID, req.ID).Build()
	currentBonusItem, err := repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).SelectOne(currentBonusItemQb)
	if err != nil {
		return nil, err
	}

	steamItem, err := issueFacade.GetSteamItemByID(s.ctx, currentBonusItem.SteamItemID)
	if err != nil {
		return nil, err
	}

	return &define.GetBonusItemWebDetailResp{
		ID:                      currentBonusItem.BonusItemID,
		IconURL:                 steamItem.IconURL,
		ImageInfos:              steamItem.ImageInfos,
		ItemName:                steamItem.ItemName,
		DetailH5:                steamItem.DetailH5,
		ExchangePrice:           currentBonusItem.ExchangePrice,
		SellPrice:               steamItem.MarketPrices.Unx.SellPrice,
		StockQty:                currentBonusItem.StockQty,
		ExchangeStartTime:       currentBonusItem.ExchangeStartTime,
		ExchangeEndTime:         currentBonusItem.ExchangeEndTime,
		PerUserLimitQty:         currentBonusItem.PerUserLimitQty,
		PerUserLimitRefreshRate: currentBonusItem.PerUserLimitRefreshRate,
		CurrentTime:             util.Now().Format(time.RFC3339),
		Status:                  currentBonusItem.Status,
	}, nil
}

// UserExchangeBonusItem 用户兑换积分商品
func (s *Service) UserExchangeBonusItem(req *define.UserExchangeBonusItemReq) (*define.UserExchangeBonusItemResp, error) {
	// 检查是否在交易时间段内
	sale, err := tmt.AllowSale(s.ctx)
	if err != nil {
		return nil, err
	}
	if !sale {
		return nil, define.BM300012Err
	}
	// 获取用户信息
	userInfo := s.GetUserFromCtx()
	if userInfo == nil {
		return nil, errors.New("获取用户信息失败")
	}
	// 获取云仓 openid
	openUserID, err := userFacade.GetOpenUserId(s.ctx, userInfo.Id)
	if err != nil {
		return nil, err
	}
	if openUserID == "" {
		return nil, define.BM300011Err
	}

	// 获取要兑换的积分商品
	bonusItemSchema := repo.GetQuery().BonusItem
	currentBonusItemQb := search.NewQueryBuilder().Eq(bonusItemSchema.BonusItemID, req.BonusItemID).Build()
	bonusItem, err := repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).SelectOne(currentBonusItemQb)
	if err != nil {
		return nil, err
	}

	// 检查是否上锁
	bonusItemLock := locker.NewBonusItemLock(util.StrVal(bonusItem.BonusItemID), locker.Exchange)
	locked, err := global.REDIS.Exists(s.ctx, bonusItemLock.GetCacheKey()).Result()
	if err != nil {
		return nil, err
	}
	if locked == 1 {
		return nil, define.BM300013Err
	}

	// 并发获取依赖数据
	deps, err := logic.GetExchangeBonusItemDeps(s.ctx, req, bonusItem, userInfo.Id)
	deps.BonusItem = bonusItem
	if deps == nil || deps.SteamItem == nil || deps.UserWallet == nil || deps.Address == nil {
		return nil, errors.New("获取兑换商品依赖数据失败")
	}
	steamItem := deps.SteamItem
	userWallet := deps.UserWallet
	address := deps.Address

	// 检查是否能兑换
	err = logic.CheckUserExchangeBonusItem(req, deps)
	if err != nil {
		return nil, err
	}

	exchangeQty := req.ExchangeQty                                      // 兑换数量
	bonusTotal := bonusItem.ExchangePrice * exchangeQty                 // 消耗积分
	costTotal := steamItem.MarketPrices.ZeroSup.SellPrice * exchangeQty // 总成本
	exchangeLogID := snowflakeutl.GenerateID()                          // 兑换记录 id
	var exchangeTime time.Time                                          // 兑换时间
	needRetry := false
	var retryReq *ycopendefine.BonusExchangeWithdrawOrderReq
	// 事务执行，跨 repo，不能使用 repo.ExecGenTx
	err = repo.GetDB().WithContext(s.ctx).Transaction(func(tx *gorm.DB) error {
		// 扣减库存
		updateStockParams := map[string]interface{}{
			"stock_qty":     gorm.Expr("stock_qty - ?", exchangeQty),
			"exchanged_qty": gorm.Expr("exchanged_qty + ?", exchangeQty),
		}
		toUpdateBonusItem := &model.BonusItem{
			BonusItemID: req.BonusItemID,
		}
		updateStockResult := tx.Model(toUpdateBonusItem).
			Where("stock_qty >= ?", exchangeQty). // 乐观锁
			Updates(updateStockParams)
		if updateStockResult.Error != nil {
			return updateStockResult.Error
		}
		if updateStockResult.RowsAffected == 0 {
			// 库存不足
			return define.BM300001Err
		}

		// 扣减积分余额
		updateUserWalletParams := map[string]interface{}{
			"bonus": gorm.Expr("bonus - ?", bonusTotal),
		}
		toUpdateUserWallet := assetmodel.UserWallet{
			UserWalletID: userWallet.UserWalletID,
		}
		updateUserWalletResult := tx.Model(toUpdateUserWallet).
			Where("bonus >= ?", bonusTotal). // 乐观锁
			Updates(updateUserWalletParams)
		if updateUserWalletResult.Error != nil {
			return updateUserWalletResult.Error
		}
		if updateUserWalletResult.RowsAffected == 0 {
			// 积分不足
			return define.BM300008Err
		}

		// 添加兑换记录
		exchangeLog := &model.BonusItemExchangeLog{
			BonusItemExchangeLogID: exchangeLogID,
			BonusItemID:            req.BonusItemID,
			SteamItemID:            bonusItem.SteamItemID,
			SkuNo:                  bonusItem.SkuNo,
			ItemName:               steamItem.ItemName,
			IconURL:                steamItem.IconURL,
			UserID:                 userInfo.Id,
			MobilePhone:            userInfo.MobilePhone,
			Nickname:               userInfo.Nickname,
			UserAvatar:             userInfo.Avatar,
			ExchangeTime:           util.Now(),
			ExchangePrice:          bonusItem.ExchangePrice,
			ExchangeQty:            exchangeQty,
			BonusTotal:             bonusTotal,
			CostTotal:              costTotal,
			Status:                 enums.ExchangeLogStatusPending.Val(),
		}
		txErr := tx.Create(exchangeLog).Error
		if txErr != nil {
			return txErr
		}

		// 添加积分消费明细
		userBonusLog := &assetmodel.UserBonusLog{
			UserID:        userInfo.Id,
			Name:          steamItem.ItemName,
			MainImg:       steamItem.IconURL,
			Source:        "兑换商品",
			Amount:        bonusTotal,
			RelateID:      util.StrVal(exchangeLog.BonusItemExchangeLogID),
			ReceiveStatus: assetenum.BonusStatusUsed.Val(),
			CreatedAt:     util.Now(),
		}
		txErr = tx.Create(userBonusLog).Error
		if txErr != nil && !util.IsMySQLDuplicateError(txErr) {
			return txErr
		}

		// 请求云仓
		createOrderReq := &ycopendefine.BonusExchangeWithdrawOrderReq{
			OpenUserId:    openUserID,
			ExchangeLogId: util.StrVal(exchangeLogID),
			ExchangeQty:   exchangeQty,
			AddressId:     req.AddressID,
			AddressInfo: &ycopendefine.WithdrawOrderAddressInfo{
				Name:        address.Name,
				MobilePhone: address.MobilePhone,
				Code:        address.Code,
				Area:        address.Area,
				Place:       address.Place,
			},
			SteamItemId: bonusItem.SteamItemID,
		}
		orderData, result, txErr := yc_open.CreateBonusExchangeOrder(s.ctx, createOrderReq)
		if txErr != nil {
			if result == yc_open.BonusExchangeLowStocks {
				// 库存不足
				return define.BM300001Err
			} else if result == yc_open.BonusExchangeUnknown {
				// 可以重试
				needRetry = true
				retryReq = createOrderReq
			} else {
				// 明确失败
				return txErr
			}
		}
		if result == yc_open.BonusExchangeSuccess {
			// 更新兑换记录
			extraData, _ := json.Marshal(createOrderReq)
			updateExLogParams := map[string]interface{}{
				"status":               enums.ExchangeLogStatusSuccess.Val(),
				"yc_withdraw_order_id": orderData.ItemWithdrawOrderID,
				"extra":                extraData,
			}
			toUpdateExLog := &model.BonusItemExchangeLog{
				BonusItemExchangeLogID: exchangeLogID,
			}
			updateExLogResult := tx.Model(toUpdateExLog).Updates(updateExLogParams)
			if updateExLogResult.Error != nil {
				return updateExLogResult.Error
			}
		}

		exchangeTime = exchangeLog.ExchangeTime
		return nil
	})

	if err != nil {
		// 兑换失败，回退限制
		rbErr := logic.RollbackUserLimit(s.ctx, bonusItem, userInfo.Id, req.ExchangeQty)
		if rbErr != nil {
			log.Ctx(s.ctx).Errorf("回退用户兑换限制失败: %v", rbErr)
		}

		return nil, err
	}

	// 异步重试
	if needRetry && retryReq != nil {
		go func() {
			isolatedCtx := context.Background()
			if span := trace.SpanFromContext(s.ctx); span != nil {
				sc := span.SpanContext()
				isolatedCtx = trace.ContextWithSpanContext(isolatedCtx, sc)
			}
			logic.RetryExchangeWithBackoff(isolatedCtx, retryReq)
		}()
	}

	return &define.UserExchangeBonusItemResp{
		ID:           exchangeLogID,
		ExchangeTime: exchangeTime,
	}, nil
}
