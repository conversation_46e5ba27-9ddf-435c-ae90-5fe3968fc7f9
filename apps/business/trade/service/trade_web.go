package service

import (
	annmodel "app_service/apps/business/announcement/dal/model"
	annrepo "app_service/apps/business/announcement/repo"
	annlogic "app_service/apps/business/announcement/service/logic"
	mcmodel "app_service/apps/business/market_changes/dal/model"
	mcrenums "app_service/apps/business/market_changes/define/enums"
	mcrepo "app_service/apps/business/market_changes/repo"
	operannmodel "app_service/apps/business/operation_announcement/dal/model"
	operannrepo "app_service/apps/business/operation_announcement/repo"
	operannlogic "app_service/apps/business/operation_announcement/service/logic"
	"app_service/apps/business/trade/define"
	"app_service/apps/business/trade/define/enums"
	"app_service/apps/business/trade/repo"
	"app_service/apps/business/trade/service/logic"
	"app_service/apps/platform/common/constant"
	commondefine "app_service/apps/platform/common/define"
	commonFacade "app_service/apps/platform/common/facade"
	"app_service/apps/platform/issue/dal/model/mongdb"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/third_party/tmt"
	"app_service/third_party/yc_open"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/go-redis/redis/v8"
)

func (s *Service) GetCirculationItemWebList(req *define.GetCirculationItemWebListReq) (*define.GetCirculationItemWebListResp, error) {
	listType := enums.CirculationItemListTypeEnum(req.Type)
	ciSchema := repo.GetQuery().CirculationItem
	qb := search.NewQueryBuilder().Eq(ciSchema.IsDisplay, enums.CirculationItemDisplayYes.Val())
	if listType == enums.CirculationItemListTypeAll {
		qb.OrderByAsc(ciSchema.IsDelisted) // 已退市的排最后
	}
	// 榜单类型和排序
	if listType == enums.CirculationItemListTypeHot && (req.OrderBy == "" || req.SortOrder == "") {
		// 热榜：先根据当天成交数量排序，再根据上一个交易日的成交数量排序
		qb.OrderByDesc(ciSchema.CurTransactionQty).OrderByDesc(ciSchema.PreTransactionQty)
	} else if listType == enums.CirculationItemListTypeTransactionAmount &&
		(req.OrderBy != enums.CirculationItemOrderByTransactionAmount || req.SortOrder == "") {
		// 成交额榜，但是排序字段没传成交额，默认按照成交额倒序排序
		qb.OrderByDesc(ciSchema.TotalTransactionAmount)
	} else if listType == enums.CirculationItemListTypeMarketAmount &&
		(req.OrderBy != enums.CirculationItemOrderByMarketAmount || req.SortOrder == "") {
		// 市值榜，当时排序字段没传市值，默认按照市值倒序排序
		qb.OrderByDesc(ciSchema.MarketAmount)
	}
	isDesc := strings.ToLower(req.SortOrder) == "desc"
	if req.OrderBy != "" && req.SortOrder != "" {
		switch req.OrderBy {
		case enums.CirculationItemOrderByTransactionAmount:
			if isDesc {
				qb.OrderByDesc(ciSchema.TotalTransactionAmount)
			} else {
				qb.OrderByAsc(ciSchema.TotalTransactionAmount)
			}
		case enums.CirculationItemOrderByMarketAmount:
			if isDesc {
				qb.OrderByDesc(ciSchema.MarketAmount)
			} else {
				qb.OrderByAsc(ciSchema.MarketAmount)
			}
		case enums.CirculationItemOrderByPriceChangeRate:
			if isDesc {
				qb.OrderByDesc(ciSchema.PriceChangeRate)
			} else {
				qb.OrderByAsc(ciSchema.PriceChangeRate)
			}
		}
	}
	// 根据主键排序，防止分页少数据
	qb.OrderByDesc(ciSchema.ID)

	// 分页处理
	offset := req.GetOffset()
	limit := req.GetPageSize()
	isHotList := req.Type == enums.CirculationItemListTypeHot.Val()
	if isHotList {
		var hotTotal = 20 // 热榜最多展示 20条
		if offset >= hotTotal {
			resp := &define.GetCirculationItemWebListResp{
				List:    make([]define.CirculationItemWebListInfo, 0),
				HasMore: false,
			}
			return resp, nil
		}
		hotLimit := hotTotal - offset
		if limit > hotLimit {
			limit = hotLimit
		}
	}
	ciDo := ciSchema.WithContext(s.ctx).Offset(offset).Limit(limit)
	// 白名单处理
	whitelistConf, err := commonFacade.GetCogCustomConfigByKey[commondefine.IssueItemWhitelist](s.ctx, "custom.issue_item_whitelist")
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetCirculationItemWebList GetIssueItemWhitelist error:%+v", err)
	} else if whitelistConf.Enable && len(whitelistConf.IssueItemIDs) > 0 {
		curUserID := s.GetUserId()
		if curUserID == "" || !util.In(curUserID, whitelistConf.UserIDs) {
			// 当前用户不在白名单中，需要过滤掉白名单中的物品
			// 实现 WHERE issue_item_id NOT IN (whitelistConf.IssueItemIDs)
			ciDo = ciDo.Not(ciSchema.IssueItemID.In(whitelistConf.IssueItemIDs...))
		}
	}
	circItems, err := repo.NewCirculationItemRepo(ciDo).
		SelectList(qb.Build())
	if err != nil {
		return nil, err
	}
	list := make([]define.CirculationItemWebListInfo, len(circItems))
	for i := 0; i < len(circItems); i++ {
		circItem := circItems[i]
		// 最新成交价优先使用缓存的值
		latestPriceCacheKey := logic.GetCirculationItemLatestPriceCacheKey(circItem.ItemID)
		cacheLatestPrice, _ := global.REDIS.Get(s.ctx, latestPriceCacheKey).Int()
		latestPrice := circItem.LatestTransactionPrice
		if cacheLatestPrice > 0 {
			latestPrice = int64(cacheLatestPrice)
		}
		// 涨跌幅优先使用缓存的值
		priceChangeRate := circItem.PriceChangeRate
		cacheRate, err := logic.GetPriceChangeRateFromCache(s.ctx, circItem.ItemID)
		if err == nil {
			priceChangeRate = float32(cacheRate)
		}
		isDelisted := false
		if circItem.IsDelisted == 1 {
			isDelisted = true
		}
		list[i] = define.CirculationItemWebListInfo{
			ID:                     circItem.ID,
			ItemID:                 circItem.ItemID,
			ItemName:               circItem.ItemName,
			ImageURL:               circItem.ImageURL,
			TotalCirculation:       circItem.TotalCirculation,
			MarketAmount:           circItem.MarketAmount,
			TransactionAmount:      circItem.TotalTransactionAmount,
			LatestTransactionPrice: latestPrice,
			PriceChangeRate:        priceChangeRate,
			IsDelisted:             isDelisted,
		}
	}

	resp := &define.GetCirculationItemWebListResp{
		List:    list,
		HasMore: len(list) == req.GetPageSize(),
	}
	return resp, nil
}

func (s *Service) GetIssueItemWebTopList(req *define.GetIssueItemWebTopListReq) (*define.GetIssueItemWebTopListResp, error) {
	// 新
	newIssueItems, err := issueFacade.GetLatestWillIssueItemList(s.ctx, 1)
	if err != nil {
		return nil, err
	}
	list := make([]define.IssueItemWebTopListInfo, 0)
	if len(newIssueItems) > 0 {
		issueItem := newIssueItems[0]
		list = append(list, define.IssueItemWebTopListInfo{
			ID:       issueItem.ID.Hex(),
			ItemID:   issueItem.ItemID.Hex(),
			ItemName: issueItem.ItemName,
			ImageURL: issueItem.ImageURL,
			Quantity: issueItem.Quantity,
			TopTag:   enums.IssueItemTagNew,
		})
	}
	// 抢
	rushIssueItems, err := issueFacade.GetLatestAlreadyIssueItemList(s.ctx, 2)
	if err != nil {
		return nil, err
	}
	for _, issueItem := range rushIssueItems {
		if len(list) >= 2 {
			break
		}

		list = append(list, define.IssueItemWebTopListInfo{
			ID:       issueItem.ID.Hex(),
			ItemID:   issueItem.ItemID.Hex(),
			ItemName: issueItem.ItemName,
			ImageURL: issueItem.ImageURL,
			Quantity: issueItem.Quantity,
			TopTag:   enums.IssueItemTagRush,
		})
	}

	resp := &define.GetIssueItemWebTopListResp{
		List: list,
	}
	return resp, nil
}

func (s *Service) GetCirculationItemWebTopList(req *define.GetCirculationItemWebTopListReq) (*define.GetCirculationItemWebTopListResp, error) {
	ciSchema := repo.GetQuery().CirculationItem
	qw := search.NewQueryBuilder().
		Eq(ciSchema.IsDisplay, enums.CirculationItemDisplayYes.Val()).
		Eq(ciSchema.IsDelisted, int32(0)). // 过滤已退市的
		OrderByDesc(ciSchema.PriceChangeRate).
		OrderByDesc(ciSchema.ID).
		Build()
	ciDo := ciSchema.WithContext(s.ctx).Limit(2)
	circItems, err := repo.NewCirculationItemRepo(ciDo).SelectList(qw)
	if err != nil {
		return nil, err
	}
	list := make([]define.CirculationItemWebTopListInfo, 0)
	for _, circItem := range circItems {
		list = append(list, define.CirculationItemWebTopListInfo{
			ID:              circItem.ID,
			ItemID:          circItem.ItemID,
			ItemName:        circItem.ItemName,
			ImageURL:        circItem.ImageURL,
			PriceChangeRate: float64(circItem.PriceChangeRate),
		})
	}

	resp := &define.GetCirculationItemWebTopListResp{
		List: list,
	}
	return resp, nil
}

func (s *Service) GetMarketWebOverview(req *define.GetMarketWebOverviewReq) (*define.GetMarketWebOverviewResp, error) {
	logPrefix := "GetMarketWebOverview"
	cacheKey := "app_service:trade:market:overview"
	cacheStr, err := global.REDIS.Get(s.ctx, cacheKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		log.Ctx(s.ctx).Errorf("%s redis Get %s failed: %v", logPrefix, cacheKey, err)
	} else if cacheStr != "" {
		data := &define.GetMarketWebOverviewResp{}
		err := json.Unmarshal([]byte(cacheStr), data)
		if err != nil {
			log.Ctx(s.ctx).Errorf("%s json.Unmarshal error: %v", logPrefix, err)
		} else {
			return data, nil
		}
	}

	now := util.Now()
	moSchema := repo.GetQuery().MarketOverview
	qw := search.NewQueryBuilder().
		Select(moSchema.TransactionAmount, moSchema.UpCount, moSchema.DownCount, moSchema.FlatCount, moSchema.UpdatedAt).
		OrderByDesc(moSchema.TradingDay).
		Build()
	moList, err := repo.NewMarketOverviewRepo(moSchema.WithContext(s.ctx).Limit(1)).SelectList(qw)
	if err != nil {
		return nil, err
	}
	timeFormatLayout := "1.2 15:04"
	resp := &define.GetMarketWebOverviewResp{
		UpdateTime: now.Format(timeFormatLayout),
	}
	if len(moList) > 0 {
		marketOverview := moList[0]
		resp.TransactionAmount = marketOverview.TransactionAmount
		resp.UpCount = marketOverview.UpCount
		resp.DownCount = marketOverview.DownCount
		resp.FlatCount = marketOverview.FlatCount
		resp.UpdateTime = marketOverview.UpdatedAt.Format(timeFormatLayout)
	}

	// 缓存
	bts, err := json.Marshal(resp)
	if err != nil {
		log.Ctx(s.ctx).Errorf("%s json.Marshal error: %v", logPrefix, err)
	} else {
		_, err = global.REDIS.SetNX(s.ctx, cacheKey, string(bts), time.Second*5).Result()
		if err != nil {
			log.Ctx(s.ctx).Errorf("%s set redis cache error: %v", logPrefix, err)
		}
	}

	return resp, nil
}

func (s *Service) GetCirculationItemWebOverview(req *define.GetCirculationItemWebOverviewReq) (*define.GetCirculationItemWebOverviewResp, error) {
	logPrefix := "GetCirculationItemWebOverview"
	// 优先用缓存读取
	now := util.Now()
	cacheKey := fmt.Sprintf("app_service:trade:circulation_item:overview:%s", now.Format("2006-01-02"))
	resultStr, err := global.REDIS.Get(s.ctx, cacheKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}
	if resultStr != "" {
		data := &define.GetCirculationItemWebOverviewResp{}
		_ = json.Unmarshal([]byte(resultStr), data)
		return data, nil
	}
	// 热榜排名第一的数据
	ciSchema := repo.GetQuery().CirculationItem
	ciQw := search.NewQueryBuilder().
		Eq(ciSchema.IsDisplay, enums.CirculationItemDisplayYes.Val()).
		OrderByDesc(ciSchema.CurTransactionQty).
		OrderByDesc(ciSchema.PreTransactionQty).
		Build()
	circItemList, err := repo.NewCirculationItemRepo(ciSchema.WithContext(s.ctx).Limit(1)).SelectList(ciQw)
	if err != nil {
		return nil, err
	}
	hotOverview := define.HotWebOverviewInfo{}
	if len(circItemList) > 0 {
		circItem := circItemList[0]
		hotOverview.ItemID = circItem.ItemID
		hotOverview.ItemName = circItem.ItemName
		// 涨跌幅
		rateMap, err := logic.GetPriceChangeRateMapByItemIDs(s.ctx, []string{circItem.ItemID})
		if err != nil {
			return nil, err
		}
		hotOverview.Rate = rateMap[circItem.ItemID]
	}
	// 概览数据
	moSchema := repo.GetQuery().MarketOverview
	qw := search.NewQueryBuilder().OrderByDesc(moSchema.TradingDay).Build()
	isHoliday, err := tmt.IsHoliday(s.ctx)
	if err != nil {
		log.Ctx(s.ctx).Errorf("%s IsHoliday error: %v", logPrefix, err)
	} else if !isHoliday {
		timeWindow := issueFacade.GetTimeWindow(s.ctx)
		if timeWindow == issueFacade.TimeWindowBeforeTrading {
			// 如果当天是交易日 且 未开市，取前一天的数据
			todayBegin := util.GetStartOfDay(now)
			qw = search.NewQueryBuilder().
				Lt(moSchema.TradingDay, todayBegin).
				OrderByDesc(moSchema.TradingDay).
				Build()
		}
	}
	moList, err := repo.NewMarketOverviewRepo(moSchema.WithContext(s.ctx).Limit(1)).SelectList(qw)
	if err != nil {
		return nil, err
	}
	transAmountOverview := define.TransactionAmountWebOverviewInfo{}
	marketAmountOverview := define.MarketAmountWebOverviewInfo{}
	if len(moList) > 0 {
		marketOverview := moList[0]
		transAmountOverview.Amount = marketOverview.TotalTransactionAmount
		transAmountOverview.Rate = marketOverview.TransactionAmountRate
		marketAmountOverview.Amount = marketOverview.TotalMarketAmount
		marketAmountOverview.Rate = marketOverview.MarketAmountRate
	} else {
		// 实时计算总成交额和总市值
		// 获取有效的流通商品
		itemIDs, err := issueFacade.GetAllValidIssueItemIDs(s.ctx)
		if err != nil {
			return nil, err
		}
		if len(itemIDs) > 0 {
			// 流通数量
			countUserItemList, err := yc_open.CountValidUserItems(s.ctx, itemIDs)
			if err != nil {
				return nil, err
			}
			// 最新收盘价
			closePriceMap, err := issueFacade.GetLatestClosePriceMapByItemIDs(s.ctx, itemIDs)
			if err != nil {
				return nil, err
			}
			// 计算总市值
			for _, item := range countUserItemList {
				closePrice := closePriceMap[item.ItemID]
				if item.Count > 0 && closePrice > 0 {
					marketAmountOverview.Amount += item.Count * int64(closePrice)
				}
			}

			// 历史累计交易额
			transAmountMap, err := issueFacade.GetTotalTransactionAmountMapByItemIDs(s.ctx, itemIDs)
			if err != nil {
				return nil, err
			}
			for _, amount := range transAmountMap {
				transAmountOverview.Amount += amount
			}
		}
	}

	resp := &define.GetCirculationItemWebOverviewResp{
		Hot:               hotOverview,
		TransactionAmount: transAmountOverview,
		MarketAmount:      marketAmountOverview,
	}
	// 缓存结果
	bts, _ := json.Marshal(resp)
	_, err = global.REDIS.Set(s.ctx, cacheKey, string(bts), time.Second*10).Result()
	if err != nil {
		log.Ctx(s.ctx).Errorf("%s redis Set %s failed: %v", logPrefix, cacheKey, err)
	}

	return resp, nil
}

func (s *Service) GetWebActivities(req *define.GetWebActivitiesReq) (*define.GetWebActivitiesResp, error) {
	logPrefix := "GetWebActivities"
	channel, _ := s.ctx.Value(constant.AppChannel).(string)
	cacheKey := fmt.Sprintf("app_service:trade:activities:%s", channel)
	cacheStr, err := global.REDIS.Get(s.ctx, cacheKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		log.Ctx(s.ctx).Errorf("%s redis Get %s failed: %v", logPrefix, cacheKey, err)
	} else if cacheStr != "" {
		data := &define.GetWebActivitiesResp{}
		err := json.Unmarshal([]byte(cacheStr), data)
		if err != nil {
			log.Ctx(s.ctx).Errorf("%s json.Unmarshal error: %v", logPrefix, err)
		} else {
			return data, nil
		}
	}

	emptyItems := make([]define.ActivityWebItem, 0)
	infoList := make([]define.ActivityWebInfo, 0)
	// 平台方公告
	annDB := annrepo.GetDB().WithContext(s.ctx)
	// 使用公共条件
	annWhere, annArgs := annlogic.GetPublishedAndChannelCondition(s.ctx)
	annDB = annDB.Where(annWhere, annArgs...)
	annDB = annDB.Order("publish_time DESC")
	var anns []*annmodel.Announcement
	err = annDB.Limit(5).Find(&anns).Error
	if err != nil {
		return nil, err
	}
	for _, item := range anns {
		infoList = append(infoList, define.ActivityWebInfo{
			ID:          item.AnnID,
			Type:        enums.ActivityTypePlatformAnn,
			Title:       item.Title,
			PublishTime: *item.PublishTime,
			Items:       emptyItems,
			IsCanJump:   item.Content != "",
		})
	}
	// 运营方公告
	operAnnDB := operannrepo.GetDB().WithContext(s.ctx)
	// 使用公共条件
	operAnnWhere, operArgs := operannlogic.GetPublishedAndChannelCondition(s.ctx)
	operAnnDB = operAnnDB.Where(operAnnWhere, operArgs...)
	operAnnDB = operAnnDB.Order("publish_time DESC")
	var operAnns []*operannmodel.OperationAnnouncement
	err = operAnnDB.Limit(5).Find(&operAnns).Error
	if err != nil {
		return nil, err
	}
	for _, item := range operAnns {
		infoList = append(infoList, define.ActivityWebInfo{
			ID:          item.OperationAnnouncementID,
			Type:        enums.ActivityTypeOperationAnn,
			Title:       item.Title,
			PublishTime: *item.PublishTime,
			Items:       emptyItems,
			IsCanJump:   item.Content != "",
		})
	}
	// 行情异动
	mcDb := mcrepo.GetDB().WithContext(s.ctx)
	mcQuery := mcDb.Table(mcmodel.TableNameMarketChanges).
		Preload("Items").
		Where("market_changes.status = ?", mcrenums.MarketChangesStatusPublished.Val()).
		Order("market_changes.publish_time DESC").
		Limit(5)
	// 添加 channel 过滤条件：使用 INNER JOIN
	mcQuery = mcQuery.Joins("INNER JOIN market_changes_channel ON market_changes.market_changes_id = market_changes_channel.market_changes_id").
		Where("market_changes_channel.channel_id = ? OR market_changes_channel.channel_id = 'all'", channel)
	mcs := make([]mcmodel.MarketChanges, 0)
	err = mcQuery.Find(&mcs).Error
	if err != nil {
		return nil, err
	}
	var itemIDs []string
	for _, mc := range mcs {
		for _, item := range mc.Items {
			if !util.In(item.ItemID, itemIDs) {
				itemIDs = append(itemIDs, item.ItemID)
			}
		}
	}
	itemMap := map[string]*mongdb.IssueItem{}
	if len(itemIDs) > 0 {
		m, err := issueFacade.GetIssueItemMapByCache(s.ctx, itemIDs)
		if err != nil {
			return nil, err
		}
		itemMap = m
	}
	for _, item := range mcs {
		// 行情异动关联的商品信息
		relateItems := make([]define.ActivityWebItem, 0)
		if len(item.Items) > 0 {
			for _, relateItem := range item.Items {
				if itemInfo, ok := itemMap[relateItem.ItemID]; ok {
					relateItems = append(relateItems, define.ActivityWebItem{
						ItemId:            relateItem.ItemID,
						ItemName:          relateItem.ItemName,
						PriceChanges:      relateItem.PriceChanges,
						CirculationStatus: itemInfo.CirculationStatus,
						Status:            itemInfo.Status,
					})
				}
			}
		}
		infoList = append(infoList, define.ActivityWebInfo{
			ID:          item.MarketChangesID,
			Type:        enums.ActivityTypeMarketChanges,
			Title:       item.Title,
			PublishTime: *item.PublishTime,
			Items:       relateItems,
			IsCanJump:   item.Content != "",
		})
	}
	// 重新排序
	sort.Slice(infoList, func(i, j int) bool {
		return infoList[i].PublishTime.After(infoList[j].PublishTime)
	})

	if len(infoList) > 5 {
		infoList = infoList[:5]
	}
	resp := &define.GetWebActivitiesResp{
		List: infoList,
	}
	// 缓存
	bts, err := json.Marshal(resp)
	if err != nil {
		log.Ctx(s.ctx).Errorf("%s json.Marshal error: %v", logPrefix, err)
	} else {
		_, err = global.REDIS.SetNX(s.ctx, cacheKey, string(bts), time.Second*5).Result()
		if err != nil {
			log.Ctx(s.ctx).Errorf("%s set redis cache error: %v", logPrefix, err)
		}
	}

	return resp, nil
}
