package service

import (
	"app_service/apps/business/trade/define"
	"app_service/apps/business/trade/repo"
	"app_service/apps/business/trade/service/logic"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"errors"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/go-redis/redis/v8"
)

func (s *Service) SyncCirculationItem(req *define.SyncCirculationItemReq) (*define.SyncCirculationItemResp, error) {
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	go func() {
		// 异步更新数据
		startTime := util.Now()
		err := logic.SyncCirculationItem(spanCtx)
		if err != nil {
			log.Ctx(spanCtx).Errorf("sync circulation item err:%v", err)
			return
		}
		log.Ctx(spanCtx).Infof("sync circulation item success! duration: %v", time.Since(startTime))
	}()

	return &define.SyncCirculationItemResp{}, nil
}

func (s *Service) UpdateMarketOverview(req *define.UpdateMarketOverviewReq) (*define.UpdateMarketOverviewResp, error) {
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	go func() {
		// 异步更新数据
		startTime := util.Now()
		err := logic.UpdateMarketOverview(spanCtx)
		if err != nil {
			log.Ctx(spanCtx).Errorf("update market overview err:%v", err)
			return
		}
		log.Ctx(spanCtx).Infof("update market overview success! duration: %v", time.Since(startTime))
	}()

	return &define.UpdateMarketOverviewResp{}, nil
}

func (s *Service) BatchUpdateCirculationItem(req *define.BatchUpdateCirculationItemReq) (*define.BatchUpdateCirculationItemResp, error) {
	logPrefix := "BatchUpdateCirculationItem"
	for {
		itemIDs, err := logic.GetFromCirculationItemUpdateQueue(s.ctx, 10)
		if err != nil && !errors.Is(err, redis.Nil) {
			return nil, err
		}

		log.Ctx(s.ctx).Debugf("%s itemIDs: %v", logPrefix, itemIDs)

		if errors.Is(err, redis.Nil) || len(itemIDs) == 0 {
			log.Ctx(s.ctx).Infof("%s no need to update", logPrefix)
			break
		}

		// 获取商品的持仓总数
		itemCountMap, err := logic.GetTotalCirculationMapFromYc(s.ctx, itemIDs)
		if err != nil {
			return nil, err
		}
		issueItemMap, err := issueFacade.GetIssueItemMapByCache(s.ctx, itemIDs)
		if err != nil {
			return nil, err
		}
		// 更新二手商品信息
		for _, itemID := range itemIDs {
			// 涨跌幅
			priceChangeRate, err := logic.GetPriceChangeRateFromCache(s.ctx, itemID)
			if err != nil {
				log.Ctx(s.ctx).Errorf("%s 获取商品涨跌幅缓存(item_id: %s)出错:%v", logPrefix, itemID, err)
				continue
			}

			ciSchema := repo.GetQuery().CirculationItem
			UpdateQw := search.NewQueryBuilder().Eq(ciSchema.ItemID, itemID).Build()
			updateParams := map[string]interface{}{
				"price_change_rate": float32(priceChangeRate),
			}

			// 最新成交价
			latestPriceCacheKey := logic.GetCirculationItemLatestPriceCacheKey(itemID)
			latestPrice, err := global.REDIS.Get(s.ctx, latestPriceCacheKey).Int64()
			if err != nil && !errors.Is(err, redis.Nil) {
				log.Ctx(s.ctx).Errorf("%s 获取最新成交价缓存(%s)出错：%v", logPrefix, latestPriceCacheKey, err)
			}
			// 计算市值
			issueItem := issueItemMap[itemID]
			if issueItem != nil && latestPrice > 0 {
				if ycItemCirculationCount, ok := itemCountMap[itemID]; ok {
					// 流通数量 = 该商品首发剩余库存(首发已结束不计算)+该商品当前总用户持仓数量(排除已提货、已融合的，持仓数量每小时计算更新一次)
					totalCirculation := ycItemCirculationCount
					now := util.Now()
					if issueItem.SaleEnd == nil || issueItem.SaleEnd.After(now) {
						totalCirculation = totalCirculation + int64(issueItem.Quantity) - int64(issueItem.SalesVolume)
					}
					// 计算市值: 流通数量 x 最新成交价
					marketAmount := totalCirculation * latestPrice
					updateParams["market_amount"] = marketAmount
				}
			}
			err = repo.NewCirculationItemRepo(ciSchema.WithContext(s.ctx)).UpdateField(updateParams, UpdateQw)
			if err != nil && !errors.Is(err, repo.UpdateFail) {
				log.Ctx(s.ctx).Errorf("%s update circulation_item err:%v", logPrefix, err)
				continue
			}
		}
	}

	return &define.BatchUpdateCirculationItemResp{}, nil
}
