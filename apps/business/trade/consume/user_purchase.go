package consume

import (
	"app_service/apps/business/trade/service/logic"
	common_constant "app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/util"
	"context"
	"strings"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/shopspring/decimal"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

const ConsumerGroup = "business_trade"

type UserPurchaseConsumer struct {
	middlewares.BaseConsumer
}

func NewUserPurchaseConsumer() *UserPurchaseConsumer {
	return &UserPurchaseConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(common_constant.UserPurchase, ConsumerGroup),
	}
}

func (o *UserPurchaseConsumer) GetTopic() string {
	return common_constant.UserPurchase
}

func (o *UserPurchaseConsumer) GetGroup() string {
	return ConsumerGroup
}

func (o *UserPurchaseConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "UserPurchaseConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[%s:%s]kafka data:%s", common_constant.UserPurchase, ConsumerGroup, util.Obj2JsonStr(event.Message()))

		// 解析消息
		var latestPrice decimal.Decimal
		var itemID string
		isCirculationItem := false
		if strings.Contains(string(event.Message().Body), "issue_item_id") {
			//一手
			data := &userPurchase4Issue{}
			err := util.JsonStr2Struct(string(event.Message().Body), data)
			if err != nil {
				log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
				return common_define.CommonWarnErr.Err(err)
			}
			latestPrice = decimal.NewFromInt32(data.Price)
			itemID = data.ItemId
		} else {
			//二手
			isCirculationItem = true
			data := &userPurchase4Sale{}
			err := util.JsonStr2Struct(string(event.Message().Body), data)
			if err != nil {
				log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
				return common_define.CommonWarnErr.Err(err)
			}
			orderCount := len(data.SuccessList)
			if orderCount == 1 {
				latestPrice = decimal.NewFromFloat32(data.OrderAmount)
			} else if orderCount > 1 {
				orderIDs := make([]string, 0)
				for _, order := range data.SuccessList {
					orderIDs = append(orderIDs, order.ID)
				}
				// 取最后一个订单的价格为最新成交价
				order, err := issueFacade.GetLatestItemWithdrawOrderByIDs(ctx, orderIDs)
				if err != nil {
					log.Ctx(ctx).Errorf("获取二手订单失败 err:%v", err)
					return common_define.CommonWarnErr.Err(err)
				}
				if order != nil && len(order.Extends.Items) > 0 {
					latestPrice = decimal.NewFromInt32(order.Extends.Items[0].SellPrice)
				}
			}
			itemID = data.ItemID
		}

		if isCirculationItem && !latestPrice.IsZero() && itemID != "" {
			// 更新最新成交价缓存
			cacheKey := logic.GetCirculationItemLatestPriceCacheKey(itemID)
			_, err := global.REDIS.Set(ctx, cacheKey, latestPrice.IntPart(), time.Hour*24).Result()
			if err != nil {
				return common_define.CommonWarnErr.Err(err)
			}
			// 更新涨跌幅
			issueItem, err := issueFacade.GetIssueItemByCache(ctx, itemID)
			if err != nil {
				return common_define.CommonWarnErr.Err(err)
			}
			isDisplay := logic.IsDisplayToCirculationItem(issueItem)
			if isDisplay {
				// 商品还在流通才缓存涨跌幅
				err = updatePriceChangeRate(ctx, itemID, latestPrice)
				if err != nil {
					return common_define.CommonWarnErr.Err(err)
				}
			}
		}

		return nil
	}

	return middlewares.SafeHandler(handler)
}

func updatePriceChangeRate(ctx context.Context, itemID string, latestPrice decimal.Decimal) error {
	// 获取上个交易日收盘价
	closePriceMap, err := issueFacade.GetPreClosePriceMapByItemIDs(ctx, []string{itemID})
	if err != nil {
		return err
	}
	issueItem, err := issueFacade.GetIssueItemByItemID(ctx, itemID)
	if err != nil {
		return err
	}
	preClosePrice := closePriceMap[itemID]

	todayLastSellPrice := latestPrice.IntPart()
	originalPriceChangeRate, err := logic.CalculateOriginalItemPriceChangeRate(ctx, issueItem, preClosePrice, int32(todayLastSellPrice))
	if err != nil {
		return err
	}
	d100 := decimal.NewFromInt32(100)
	priceChangeRate, _ := originalPriceChangeRate.Mul(d100).Round(2).Float64()
	// 更新涨跌幅缓存
	err = logic.CachePriceChangeRateForCirculationItem(ctx, itemID, priceChangeRate)
	if err != nil {
		log.Ctx(ctx).Errorf("cache price_change_rate error: %v", err)
	}

	// 添加到更新队列
	err = logic.AddToCirculationItemUpdateQueue(ctx, itemID)
	if err != nil {
		log.Ctx(ctx).Errorf("updatePriceChangeRate AddToCirculationItemUpdateQueue error: %v", err)
	}

	return nil
}

type userPurchase4Issue struct {
	ID          string    `json:"_id"`
	UserId      string    `json:"user_id"`
	IssueItemId string    `json:"issue_item_id"`
	ItemId      string    `json:"item_id"`
	PayAmount   int32     `json:"pay_amount"`
	PayTime     time.Time `json:"pay_time"`
	Price       int32     `json:"price"`
	Quantity    int32     `json:"quantity"`
}

type successWithdrawOrder struct {
	ID                 string   `json:"itemWithdrawOrderId"`
	AcquireUserItemIDs []string `json:"acquireUserItemIds"`
}
type userPurchase4Sale struct {
	SaleBatchId string                 `json:"sale_batch_id"`
	BuyUserId   string                 `json:"buy_user_id"`
	ItemID      string                 `json:"item_id"`
	OrderAmount float32                `json:"order_amount"`
	SuccessList []successWithdrawOrder `json:"successList"`
	UniId       string                 `json:"uni_id"`
}
