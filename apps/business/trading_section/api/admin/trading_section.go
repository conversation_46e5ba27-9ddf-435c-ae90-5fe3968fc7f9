package admin

import (
	"app_service/apps/business/trading_section/define"
	"app_service/apps/business/trading_section/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// AddTradingSection
// @Summary 新增板块
// @Description 新增板块
// @Tags 管理端-板块分区
// @x-apifox-folder "管理端/板块分区"
// @Param data body define.AddTradingSectionReq true "新增参数"
// @Success 200 {object} response.Data{data=define.AddTradingSectionResp}
// @Router  /admin/v1/trading_section/add [POST]
// @Security Bearer
func AddTradingSection(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddTradingSectionReq{}, s.AddTradingSection)
}

// EditTradingSection
// @Summary 编辑板块
// @Description 编辑板块
// @Tags 管理端-板块分区
// @x-apifox-folder "管理端/板块分区"
// @Param data body define.EditTradingSectionReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditTradingSectionResp}
// @Router  /admin/v1/trading_section/edit [POST]
// @Security Bearer
func EditTradingSection(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditTradingSectionReq{}, s.EditTradingSection)
}

// GetAdminTradingSectionDetail
// @Summary 获取板块详情
// @Description 获取板块详情
// @Tags 管理端-板块分区
// @x-apifox-folder "管理端/板块分区"
// @Param data query define.GetAdminTradingSectionDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetAdminTradingSectionDetailResp}
// @Router  /admin/v1/trading_section/detail [GET]
// @Security Bearer
func GetAdminTradingSectionDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAdminTradingSectionDetailReq{}, s.GetAdminTradingSectionDetail)
}

// UpdateTradingSectionStatus
// @Summary 更新板块状态
// @Description 更新板块状态
// @Tags 管理端-板块分区
// @x-apifox-folder "管理端/板块分区"
// @Param data body define.UpdateTradingSectionStatusReq true "更新参数"
// @Success 200 {object} response.Data{data=define.UpdateTradingSectionStatusResp}
// @Router  /admin/v1/trading_section/update_status [POST]
// @Security Bearer
func UpdateTradingSectionStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.UpdateTradingSectionStatusReq{}, s.UpdateTradingSectionStatus)
}

// UpdateTradingSectionPriority
// @Summary 更新板块优先级
// @Description 更新板块优先级
// @Tags 管理端-板块分区
// @x-apifox-folder "管理端/板块分区"
// @Param data body define.UpdateTradingSectionPriorityReq true "更新参数"
// @Success 200 {object} response.Data{data=define.UpdateTradingSectionPriorityResp}
// @Router  /admin/v1/trading_section/update_priority [POST]
// @Security Bearer
func UpdateTradingSectionPriority(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.UpdateTradingSectionPriorityReq{}, s.UpdateTradingSectionPriority)
}

// GetAdminTradingSectionList
// @Summary 获取板块列表
// @Description 获取板块列表
// @Tags 管理端-板块分区
// @x-apifox-folder "管理端/板块分区"
// @Param data query define.GetAdminTradingSectionListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetAdminTradingSectionListResp}
// @Router  /admin/v1/trading_section/list [GET]
// @Security Bearer
func GetAdminTradingSectionList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAdminTradingSectionListReq{}, s.GetAdminTradingSectionList)
}

// RefreshTradingSectionDailyStat
// @Summary 刷新图表数据
// @Description 刷新图表数据
// @Tags 管理端-板块分区
// @x-apifox-folder "管理端/板块分区"
// @Param data body define.RefreshTradingSectionDailyStatReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.RefreshTradingSectionDailyStatResp}
// @Router  /admin/v1/trading_section/refresh_daily_stat [POST]
// @Security Bearer
func RefreshTradingSectionDailyStat(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.RefreshTradingSectionDailyStatReq{}, s.RefreshTradingSectionDailyStat)
}
