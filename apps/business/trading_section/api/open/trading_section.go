package open

import (
	"app_service/apps/business/trading_section/define"
	"app_service/apps/business/trading_section/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// UpdateTradingSectionStatData
// @Summary 更新板块统计数据
// @Description 更新板块统计数据
// @Tags Open端-板块分区
// @x-apifox-folder "Open端/板块分区"
// @Param data body define.UpdateTradingSectionStatDataReq true "新增参数"
// @Success 200 {object} response.Data{data=define.UpdateTradingSectionStatDataResp}
// @Router  /open/v1/trading_section/update_stat_data [POST]
// @Security Bearer
func UpdateTradingSectionStatData(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.UpdateTradingSectionStatDataReq{}, s.UpdateTradingSectionStatData)
}

// TradingSectionDailyStat
// @Summary 更新板块每日统计数据
// @Description 更新板块每日统计数据
// @Tags Open端-板块分区
// @x-apifox-folder "Open端/板块分区"
// @Param data body define.TradingSectionDailyStatReq true "新增参数"
// @Success 200 {object} response.Data{data=define.TradingSectionDailyStatResp}
// @Router  /open/v1/trading_section/daily_stat [POST]
// @Security Bearer
func TradingSectionDailyStat(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.TradingSectionDailyStatReq{}, s.TradingSectionDailyStat)
}
