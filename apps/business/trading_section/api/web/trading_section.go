package web

import (
	"app_service/apps/business/trading_section/define"
	"app_service/apps/business/trading_section/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetWebTradingSectionList
// @Summary 获取板块列表
// @Description 获取板块列表
// @Tags 用户端-板块分区
// @x-apifox-folder "用户端/板块分区"
// @Param data query define.GetWebTradingSectionListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebTradingSectionListResp}
// @Router  /web/v1/trading_section/list [GET]
// @Security Bearer
func GetWebTradingSectionList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebTradingSectionListReq{}, s.GetWebTradingSectionList)
}

// GetWebTradingSectionDetail
// @Summary 获取板块详情
// @Description 获取板块详情
// @Tags 用户端-板块分区
// @x-apifox-folder "用户端/板块分区"
// @Param data query define.GetWebTradingSectionDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebTradingSectionDetailResp}
// @Router  /web/v1/trading_section/detail [GET]
// @Security Bearer
func GetWebTradingSectionDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebTradingSectionDetailReq{}, s.GetWebTradingSectionDetail)
}

// GetWebTradingSectionActivities
// @Summary 获取板块活动列表
// @Description 获取板块活动列表
// @Tags 用户端-板块分区
// @x-apifox-folder "用户端/板块分区"
// @Param data query define.GetWebTradingSectionActivitiesReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebTradingSectionActivitiesResp}
// @Router  /web/v1/trading_section/activities [GET]
// @Security Bearer
func GetWebTradingSectionActivities(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebTradingSectionActivitiesReq{}, s.GetWebTradingSectionActivities)
}

// GetWebTradingSectionItemList
// @Summary 获取板块商品列表
// @Description 获取板块商品列表
// @Tags 用户端-板块分区
// @x-apifox-folder "用户端/板块分区"
// @Param data query define.GetWebTradingSectionItemListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebTradingSectionItemListResp}
// @Router  /web/v1/trading_section/item_list [GET]
// @Security Bearer
func GetWebTradingSectionItemList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebTradingSectionItemListReq{}, s.GetWebTradingSectionItemList)
}
