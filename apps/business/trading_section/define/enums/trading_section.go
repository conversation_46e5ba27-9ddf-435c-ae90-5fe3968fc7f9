package enums

type TradingSectionStatusEnum int32

func (r TradingSectionStatusEnum) Val() int32 {
	return int32(r)
}

const (
	TradingSectionStatusDisabled TradingSectionStatusEnum = 0 // 关闭
	TradingSectionStatusEnabled  TradingSectionStatusEnum = 1 // 启用
)

const (
	ActivityTypePlatformAnn   = "platform_announcement"
	ActivityTypeOperationAnn  = "operation_announcement"
	ActivityTypeMarketChanges = "market_changes"
)
