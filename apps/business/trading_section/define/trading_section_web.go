package define

import (
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/pkg/pagination"
	"time"
)

type (
	GetWebTradingSectionListReq struct {
		pagination.Pagination
	}
	GetWebTradingSectionListItemInfo struct {
		ItemID   string `json:"item_id"`   // 商品 id
		ItemName string `json:"item_name"` // 商品名称
		ImageURL string `json:"image_url"` // 商品图片
	}
	GetWebClosePriceDailyData struct {
		Date            string  `json:"date"`              // 日期
		PriceChangeRate float32 `json:"price_change_rate"` // 涨跌幅
	}
	GetWebTradingSectionListInfo struct {
		ID                 int64                               `json:"id,string"`            // 板块 id
		Name               string                              `json:"name"`                 // 板块名称
		ItemTotal          int64                               `json:"item_total"`           // 商品总数
		TopItemList        []*GetWebTradingSectionListItemInfo `json:"top_item_list"`        // 涨跌幅最高的商品列表（数量<=3）
		PriceChangeRate    float32                             `json:"price_change_rate"`    // 涨跌幅
		ClosePriceTrending []*GetWebClosePriceDailyData        `json:"close_price_trending"` // 收盘价涨跌幅趋势
	}
	GetWebTradingSectionListResp struct {
		List  []*GetWebTradingSectionListInfo `json:"list"`  // 板块列表
		Total int64                           `json:"total"` // 板块总数
	}
)

type (
	GetWebTradingSectionDetailReq struct {
		ID int64 `form:"id" json:"id,string" binding:"required"` // 板块 id
	}
	GetWebTradingSectionDetailResp struct {
		ID                        int64   `json:"id,string"`                   // 板块 id
		Name                      string  `json:"name"`                        // 板块名称
		Description               string  `json:"description"`                 // 板块介绍
		PriceChangeRate           float32 `json:"price_change_rate"`           // 板块涨跌幅
		ItemTotal                 int64   `json:"item_total"`                  // 商品总数
		TransactionAmount         int64   `json:"transaction_amount"`          // 成交额（单位：分）
		TransactionAmountRatio    float32 `json:"transaction_amount_ratio"`    // 成交额百分比
		TransactionAmountTrending []int64 `json:"transaction_amount_trending"` // 成交额趋势，每个数组元素是一天的成交额（单位：分）
		MarketAmount              int64   `json:"market_amount"`               // 市值（单位：分）
		MarketAmountRatio         float32 `json:"market_amount_ratio"`         // 市值百分比
		MarketAmountTrending      []int64 `json:"market_amount_trending"`      // 市值趋势，每个数组元素是一天的市值（单位：分）
		StatDataUpdateTime        string  `json:"stat_data_update_time"`       // 统计数据更新时间，格式：8.13 15:31
	}
)

type (
	GetWebTradingSectionActivitiesReq struct {
		ID int64 `form:"id" json:"id,string" binding:"required"` // 板块 id
	}
	GetWebActivityItem struct {
		ItemId            string                       `json:"item_id"`            // 商品ID
		ItemName          string                       `json:"item_name"`          // 商品名称
		PriceChanges      float64                      `json:"price_changes"`      // 价格变动
		CirculationStatus mongdb.CirculationStatusEnum `json:"circulation_status"` // 流通状态
		Status            mongdb.IssueItemStatusEnum   `json:"status"`             // 状态
	}
	GetWebActivityData struct {
		ID          int64                `json:"id,string"`
		Type        string               `json:"type"`         // 类型，platform_announcement: 平台方公告，operation_announcement: 运营方公告，market_changes: 行情异动
		Title       string               `json:"title"`        // 标题
		PublishTime time.Time            `json:"publish_time"` // 发布时间
		Items       []GetWebActivityItem `json:"items"`        // 商品列表（仅行情异动）
		IsCanJump   bool                 `json:"is_can_jump"`  // 是否可跳转
	}
	GetWebTradingSectionActivitiesResp struct {
		List []GetWebActivityData `json:"list"`
	}
)

type (
	GetWebTradingSectionItemListReq struct {
		pagination.Pagination
		ID        int64  `form:"id" json:"id,string" binding:"required"` // 板块 id
		OrderBy   string `form:"order_by" json:"order_by"`               // 排序字段，transaction_amount(成交额) | market_amount(市值) | price_change_rate(涨跌幅)
		SortOrder string `form:"sort_order" json:"sort_order"`           // 排序方式，asc(升序) | desc(降序)
	}
	GetWebTradingSectionItemInfo struct {
		ID                     int64   `json:"id,string"`                // 主键 id
		ItemID                 string  `json:"item_id"`                  // 商品 id
		ItemName               string  `json:"item_name"`                // 商品名称
		ImageURL               string  `json:"image_url"`                // 商品图片
		TotalCirculation       int64   `json:"total_circulation"`        // 流通数量
		MarketAmount           int64   `json:"market_amount"`            // 市值（单位：分）
		LatestTransactionPrice int64   `json:"latest_transaction_price"` // 最新成交价（单位：分）
		TransactionAmount      int64   `json:"transaction_amount"`       // 成交额（单位：分）
		PriceChangeRate        float32 `json:"price_change_rate"`        // 涨跌幅
		IsDelisted             bool    `json:"is_delisted"`              // 是否退市，true：已退市
	}
	GetWebTradingSectionItemListResp struct {
		List    []GetWebTradingSectionItemInfo `json:"list"`
		HasMore bool                           `json:"has_more"`
	}
)
