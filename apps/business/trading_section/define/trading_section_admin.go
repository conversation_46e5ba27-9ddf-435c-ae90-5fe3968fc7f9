package define

import (
	"app_service/pkg/pagination"
	"time"
)

type (
	AddTradingSectionReq struct {
		Name        string `json:"name" binding:"required"`        // 板块名称
		Description string `json:"description" binding:"required"` // 板块介绍
		Priority    int32  `json:"priority"`                       // 优先级
	}
	AddTradingSectionResp struct {
		ID        int64  `json:"id,string"`  // 板块主键 id
		CreatedAt string `json:"created_at"` // 创建时间
	}
)

type (
	EditTradingSectionReq struct {
		ID          int64    `json:"id,string" binding:"required"`   // 板块 id
		Name        string   `json:"name" binding:"required"`        // 板块名称
		Description string   `json:"description" binding:"required"` // 板块介绍
		Priority    int32    `json:"priority" binding:"required"`    // 优先级
		ItemIDs     []string `json:"item_ids" binding:"required"`    // 商品 id 列表
	}
	EditTradingSectionResp struct {
		ID        int64  `json:"id,string"`  // 板块主键 id
		UpdatedAt string `json:"updated_at"` // 更新时间
	}
)

type (
	GetAdminTradingSectionDetailReq struct {
		ID int64 `form:"id,string" json:"id,string" binding:"required"` // 板块 id
	}
	GetAdminTradingSectionDetailItemInfo struct {
		ItemID          string   `json:"item_id"`           // 商品 id/挂牌编码
		ItemName        string   `json:"item_name"`         // 商品名称
		ImageURL        string   `json:"image_url"`         // 商品主图
		IPClassifyNames []string `json:"ip_classify_names"` // 商品 IP 名称列表
		HolderQuantity  int64    `json:"holder_quantity"`   // 流通数量
		MarketAmount    int64    `json:"market_amount"`     // 市值（单位：分）
		LastOrderAmount int32    `json:"last_order_amount"` // 最近成交价（单位：分）
	}
	GetAdminTradingSectionDetailResp struct {
		ID          int64                                   `json:"id,string"`   // 板块主键 id
		Name        string                                  `json:"name"`        // 板块名称
		Description string                                  `json:"description"` // 板块介绍
		Priority    int32                                   `json:"priority"`    // 优先级
		ItemList    []*GetAdminTradingSectionDetailItemInfo `json:"item_list"`   // 商品列表
		Status      int32                                   `json:"status"`      // 状态，0：关闭，1：开启
		CreatedAt   time.Time                               `json:"created_at"`  // 创建时间
		UpdatedAt   time.Time                               `json:"updated_at"`  // 更新时间
	}
)

type (
	UpdateTradingSectionStatusReq struct {
		ID     int64  `json:"id,string" binding:"required"`        // 板块 id
		Status *int32 `json:"status" binding:"required,oneof=0 1"` // 状态，0：关闭，1：开启
	}
	UpdateTradingSectionStatusResp struct {
		ID        int64  `json:"id,string"`  // 板块主键 id
		UpdatedAt string `json:"updated_at"` // 更新时间
	}
)

type (
	UpdateTradingSectionPriorityReq struct {
		ID       int64 `json:"id,string" binding:"required"` // 板块 id
		Priority int32 `json:"priority" binding:"required"`  // 优先级
	}
	UpdateTradingSectionPriorityResp struct {
		ID        int64  `json:"id,string"`  // 板块主键 id
		UpdatedAt string `json:"updated_at"` // 更新时间
	}
)

type (
	GetAdminTradingSectionListReq struct {
		pagination.Pagination
		ID       int64  `form:"id,string" json:"id,string"` // 板块 id
		Name     string `form:"name" json:"name"`           // 板块名称
		ItemID   string `form:"item_id" json:"item_id"`     // 商品 id/挂牌编码
		ItemName string `form:"item_name" json:"item_name"` // 商品名称
		Status   *int32 `form:"status" json:"status"`       // 状态，0：关闭，1：开启
	}
	GetAdminTradingSectionListInfo struct {
		ID                int64     `json:"id,string"`          // 板块主键 id
		Name              string    `json:"name"`               // 板块名称
		Description       string    `json:"description"`        // 板块介绍
		Priority          int32     `json:"priority"`           // 优先级
		ItemQty           int64     `json:"item_qty"`           // 商品数量
		PriceChangeRate   float32   `json:"price_change_rate"`  // 涨跌幅
		TransactionAmount int64     `json:"transaction_amount"` // 成交额（单位：分）
		MarketAmount      int64     `json:"market_amount"`      // 市值（单位：分）
		Status            int32     `json:"status"`             // 状态，0：关闭，1：开启
		CreatedAt         time.Time `json:"created_at"`         // 创建时间
		UpdatedAt         time.Time `json:"updated_at"`         // 更新时间
	}
	GetAdminTradingSectionListResp struct {
		List  []*GetAdminTradingSectionListInfo `json:"list"`  // 列表数据
		Total int64                             `json:"total"` // 总数
	}
)

type (
	RefreshTradingSectionDailyStatReq struct {
		ID int64 `json:"id,string" binding:"required"` // 板块 id
	}
	RefreshTradingSectionDailyStatResp struct {
		ID int64 `json:"id,string"` // 板块主键 id
	}
)
