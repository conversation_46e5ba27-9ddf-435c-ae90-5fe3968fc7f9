// Code generated by app_service/gen/tool. DO NOT EDIT.

package repo

import (
	"app_service/apps/business/trading_section/dal/model"
	"app_service/apps/business/trading_section/dal/query"
	"app_service/pkg/search"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type AnnTradingSectionRepository struct {
	do query.IAnnTradingSectionDo
}

func NewAnnTradingSectionRepo(do query.IAnnTradingSectionDo) *AnnTradingSectionRepository {
	return &AnnTradingSectionRepository{
		do: do,
	}
}

func (r *AnnTradingSectionRepository) SelectOne(queryWrapper *search.QueryWrapper) (*model.AnnTradingSection, error) {
	if queryWrapper != nil {
		if len(queryWrapper.ScopeOpts) != 0 {
			r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
		}
		if len(queryWrapper.SelectFields) != 0 {
			r.do = r.do.Select(queryWrapper.SelectFields...)
		}
		if len(queryWrapper.OrderBy) != 0 {
			r.do = r.do.Order(queryWrapper.OrderBy...)
		}
	}

	records, err := r.do.Find()
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	if len(records) > 1 {
		return nil, errors.New("more than one item found")
	}
	return records[0], nil
}

func (r *AnnTradingSectionRepository) SelectList(queryWrapper *search.QueryWrapper) ([]*model.AnnTradingSection, error) {
	if queryWrapper != nil {
		if len(queryWrapper.ScopeOpts) != 0 {
			r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
		}
		if len(queryWrapper.SelectFields) != 0 {
			r.do = r.do.Select(queryWrapper.SelectFields...)
		}
		if len(queryWrapper.OrderBy) != 0 {
			r.do = r.do.Order(queryWrapper.OrderBy...)
		}
	}

	records, err := r.do.Find()
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (r *AnnTradingSectionRepository) SelectPage(queryWrapper *search.QueryWrapper, pageIndex int, pageSize int) ([]*model.AnnTradingSection, int64, error) {
	if queryWrapper != nil {
		if len(queryWrapper.ScopeOpts) != 0 {
			r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
		}
		if len(queryWrapper.SelectFields) != 0 {
			r.do = r.do.Select(queryWrapper.SelectFields...)
		}
		if len(queryWrapper.OrderBy) != 0 {
			r.do = r.do.Order(queryWrapper.OrderBy...)
		}
	}
	records, count, err := r.do.FindByPage(search.Paginate(pageSize, pageIndex))
	if err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

func (r *AnnTradingSectionRepository) Count(queryWrapper *search.QueryWrapper) (int64, error) {
	if queryWrapper != nil {
		r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
	}
	count, err := r.do.Count()
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (r *AnnTradingSectionRepository) Save(model *model.AnnTradingSection) error {
	err := r.do.Create(model)
	if err != nil {
		return err
	}
	return nil
}

func (r *AnnTradingSectionRepository) BatchSave(models []*model.AnnTradingSection, batchSize int) error {
	err := r.do.CreateInBatches(models, batchSize)
	if err != nil {
		return err
	}
	return nil
}

func (r *AnnTradingSectionRepository) UpdateById(model *model.AnnTradingSection) error {
	result, err := r.do.Updates(model)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *AnnTradingSectionRepository) Update(ms *model.AnnTradingSection, queryWrapper *search.QueryWrapper) error {
	if queryWrapper != nil {
		r.do = r.do.Scopes(
			search.MakeOpt(queryWrapper.ScopeOpts...),
		)
	}
	result, err := r.do.Updates(ms)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *AnnTradingSectionRepository) UpdateField(params interface{}, queryWrapper *search.QueryWrapper) error {
	if queryWrapper != nil {
		r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
	}
	result, err := r.do.Updates(params)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *AnnTradingSectionRepository) RemoveByIds(ms ...*model.AnnTradingSection) error {
	result, err := r.do.Delete(ms...)
	if err != nil {
		return err
	} else if result.RowsAffected != int64(len(ms)) {
		return UpdateFail
	}
	return nil
}
