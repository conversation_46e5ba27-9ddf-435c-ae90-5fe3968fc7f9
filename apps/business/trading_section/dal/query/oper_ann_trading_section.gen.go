// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/trading_section/dal/model"
)

func newOperAnnTradingSection(db *gorm.DB, opts ...gen.DOOption) operAnnTradingSection {
	_operAnnTradingSection := operAnnTradingSection{}

	_operAnnTradingSection.operAnnTradingSectionDo.UseDB(db, opts...)
	_operAnnTradingSection.operAnnTradingSectionDo.UseModel(&model.OperAnnTradingSection{})

	tableName := _operAnnTradingSection.operAnnTradingSectionDo.TableName()
	_operAnnTradingSection.ALL = field.NewAsterisk(tableName)
	_operAnnTradingSection.OperAnnID = field.NewInt64(tableName, "oper_ann_id")
	_operAnnTradingSection.TradingSectionID = field.NewInt64(tableName, "trading_section_id")
	_operAnnTradingSection.TradingSectionName = field.NewString(tableName, "trading_section_name")
	_operAnnTradingSection.CreatedAt = field.NewTime(tableName, "created_at")
	_operAnnTradingSection.UpdatedAt = field.NewTime(tableName, "updated_at")

	_operAnnTradingSection.fillFieldMap()

	return _operAnnTradingSection
}

// operAnnTradingSection 运营方公告-交易板块关联表
type operAnnTradingSection struct {
	operAnnTradingSectionDo

	ALL                field.Asterisk
	OperAnnID          field.Int64  // 运营方公告表 id (operation_announcement.operation_announcement_id)
	TradingSectionID   field.Int64  // 交易板块表 id (trading_section.id)
	TradingSectionName field.String // 交易板块名称(trading_section.name)
	CreatedAt          field.Time   // 创建时间
	UpdatedAt          field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (o operAnnTradingSection) Table(newTableName string) *operAnnTradingSection {
	o.operAnnTradingSectionDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o operAnnTradingSection) As(alias string) *operAnnTradingSection {
	o.operAnnTradingSectionDo.DO = *(o.operAnnTradingSectionDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *operAnnTradingSection) updateTableName(table string) *operAnnTradingSection {
	o.ALL = field.NewAsterisk(table)
	o.OperAnnID = field.NewInt64(table, "oper_ann_id")
	o.TradingSectionID = field.NewInt64(table, "trading_section_id")
	o.TradingSectionName = field.NewString(table, "trading_section_name")
	o.CreatedAt = field.NewTime(table, "created_at")
	o.UpdatedAt = field.NewTime(table, "updated_at")

	o.fillFieldMap()

	return o
}

func (o *operAnnTradingSection) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *operAnnTradingSection) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 5)
	o.fieldMap["oper_ann_id"] = o.OperAnnID
	o.fieldMap["trading_section_id"] = o.TradingSectionID
	o.fieldMap["trading_section_name"] = o.TradingSectionName
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
}

func (o operAnnTradingSection) clone(db *gorm.DB) operAnnTradingSection {
	o.operAnnTradingSectionDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o operAnnTradingSection) replaceDB(db *gorm.DB) operAnnTradingSection {
	o.operAnnTradingSectionDo.ReplaceDB(db)
	return o
}

type operAnnTradingSectionDo struct{ gen.DO }

type IOperAnnTradingSectionDo interface {
	gen.SubQuery
	Debug() IOperAnnTradingSectionDo
	WithContext(ctx context.Context) IOperAnnTradingSectionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IOperAnnTradingSectionDo
	WriteDB() IOperAnnTradingSectionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IOperAnnTradingSectionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IOperAnnTradingSectionDo
	Not(conds ...gen.Condition) IOperAnnTradingSectionDo
	Or(conds ...gen.Condition) IOperAnnTradingSectionDo
	Select(conds ...field.Expr) IOperAnnTradingSectionDo
	Where(conds ...gen.Condition) IOperAnnTradingSectionDo
	Order(conds ...field.Expr) IOperAnnTradingSectionDo
	Distinct(cols ...field.Expr) IOperAnnTradingSectionDo
	Omit(cols ...field.Expr) IOperAnnTradingSectionDo
	Join(table schema.Tabler, on ...field.Expr) IOperAnnTradingSectionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IOperAnnTradingSectionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IOperAnnTradingSectionDo
	Group(cols ...field.Expr) IOperAnnTradingSectionDo
	Having(conds ...gen.Condition) IOperAnnTradingSectionDo
	Limit(limit int) IOperAnnTradingSectionDo
	Offset(offset int) IOperAnnTradingSectionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IOperAnnTradingSectionDo
	Unscoped() IOperAnnTradingSectionDo
	Create(values ...*model.OperAnnTradingSection) error
	CreateInBatches(values []*model.OperAnnTradingSection, batchSize int) error
	Save(values ...*model.OperAnnTradingSection) error
	First() (*model.OperAnnTradingSection, error)
	Take() (*model.OperAnnTradingSection, error)
	Last() (*model.OperAnnTradingSection, error)
	Find() ([]*model.OperAnnTradingSection, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.OperAnnTradingSection, err error)
	FindInBatches(result *[]*model.OperAnnTradingSection, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.OperAnnTradingSection) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IOperAnnTradingSectionDo
	Assign(attrs ...field.AssignExpr) IOperAnnTradingSectionDo
	Joins(fields ...field.RelationField) IOperAnnTradingSectionDo
	Preload(fields ...field.RelationField) IOperAnnTradingSectionDo
	FirstOrInit() (*model.OperAnnTradingSection, error)
	FirstOrCreate() (*model.OperAnnTradingSection, error)
	FindByPage(offset int, limit int) (result []*model.OperAnnTradingSection, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IOperAnnTradingSectionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (o operAnnTradingSectionDo) Debug() IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Debug())
}

func (o operAnnTradingSectionDo) WithContext(ctx context.Context) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o operAnnTradingSectionDo) ReadDB() IOperAnnTradingSectionDo {
	return o.Clauses(dbresolver.Read)
}

func (o operAnnTradingSectionDo) WriteDB() IOperAnnTradingSectionDo {
	return o.Clauses(dbresolver.Write)
}

func (o operAnnTradingSectionDo) Session(config *gorm.Session) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Session(config))
}

func (o operAnnTradingSectionDo) Clauses(conds ...clause.Expression) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o operAnnTradingSectionDo) Returning(value interface{}, columns ...string) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o operAnnTradingSectionDo) Not(conds ...gen.Condition) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o operAnnTradingSectionDo) Or(conds ...gen.Condition) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o operAnnTradingSectionDo) Select(conds ...field.Expr) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o operAnnTradingSectionDo) Where(conds ...gen.Condition) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o operAnnTradingSectionDo) Order(conds ...field.Expr) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o operAnnTradingSectionDo) Distinct(cols ...field.Expr) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o operAnnTradingSectionDo) Omit(cols ...field.Expr) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o operAnnTradingSectionDo) Join(table schema.Tabler, on ...field.Expr) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o operAnnTradingSectionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o operAnnTradingSectionDo) RightJoin(table schema.Tabler, on ...field.Expr) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o operAnnTradingSectionDo) Group(cols ...field.Expr) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o operAnnTradingSectionDo) Having(conds ...gen.Condition) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o operAnnTradingSectionDo) Limit(limit int) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o operAnnTradingSectionDo) Offset(offset int) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o operAnnTradingSectionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o operAnnTradingSectionDo) Unscoped() IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Unscoped())
}

func (o operAnnTradingSectionDo) Create(values ...*model.OperAnnTradingSection) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o operAnnTradingSectionDo) CreateInBatches(values []*model.OperAnnTradingSection, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o operAnnTradingSectionDo) Save(values ...*model.OperAnnTradingSection) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o operAnnTradingSectionDo) First() (*model.OperAnnTradingSection, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperAnnTradingSection), nil
	}
}

func (o operAnnTradingSectionDo) Take() (*model.OperAnnTradingSection, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperAnnTradingSection), nil
	}
}

func (o operAnnTradingSectionDo) Last() (*model.OperAnnTradingSection, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperAnnTradingSection), nil
	}
}

func (o operAnnTradingSectionDo) Find() ([]*model.OperAnnTradingSection, error) {
	result, err := o.DO.Find()
	return result.([]*model.OperAnnTradingSection), err
}

func (o operAnnTradingSectionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.OperAnnTradingSection, err error) {
	buf := make([]*model.OperAnnTradingSection, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o operAnnTradingSectionDo) FindInBatches(result *[]*model.OperAnnTradingSection, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o operAnnTradingSectionDo) Attrs(attrs ...field.AssignExpr) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o operAnnTradingSectionDo) Assign(attrs ...field.AssignExpr) IOperAnnTradingSectionDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o operAnnTradingSectionDo) Joins(fields ...field.RelationField) IOperAnnTradingSectionDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o operAnnTradingSectionDo) Preload(fields ...field.RelationField) IOperAnnTradingSectionDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o operAnnTradingSectionDo) FirstOrInit() (*model.OperAnnTradingSection, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperAnnTradingSection), nil
	}
}

func (o operAnnTradingSectionDo) FirstOrCreate() (*model.OperAnnTradingSection, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperAnnTradingSection), nil
	}
}

func (o operAnnTradingSectionDo) FindByPage(offset int, limit int) (result []*model.OperAnnTradingSection, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o operAnnTradingSectionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o operAnnTradingSectionDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o operAnnTradingSectionDo) Delete(models ...*model.OperAnnTradingSection) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *operAnnTradingSectionDo) withDO(do gen.Dao) *operAnnTradingSectionDo {
	o.DO = *do.(*gen.DO)
	return o
}
