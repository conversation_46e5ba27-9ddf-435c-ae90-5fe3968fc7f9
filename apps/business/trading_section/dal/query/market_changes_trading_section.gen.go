// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/trading_section/dal/model"
)

func newMarketChangesTradingSection(db *gorm.DB, opts ...gen.DOOption) marketChangesTradingSection {
	_marketChangesTradingSection := marketChangesTradingSection{}

	_marketChangesTradingSection.marketChangesTradingSectionDo.UseDB(db, opts...)
	_marketChangesTradingSection.marketChangesTradingSectionDo.UseModel(&model.MarketChangesTradingSection{})

	tableName := _marketChangesTradingSection.marketChangesTradingSectionDo.TableName()
	_marketChangesTradingSection.ALL = field.NewAsterisk(tableName)
	_marketChangesTradingSection.MarketChangesID = field.NewInt64(tableName, "market_changes_id")
	_marketChangesTradingSection.TradingSectionID = field.NewInt64(tableName, "trading_section_id")
	_marketChangesTradingSection.TradingSectionName = field.NewString(tableName, "trading_section_name")
	_marketChangesTradingSection.CreatedAt = field.NewTime(tableName, "created_at")
	_marketChangesTradingSection.UpdatedAt = field.NewTime(tableName, "updated_at")

	_marketChangesTradingSection.fillFieldMap()

	return _marketChangesTradingSection
}

// marketChangesTradingSection 行情异动-交易板块关联表
type marketChangesTradingSection struct {
	marketChangesTradingSectionDo

	ALL                field.Asterisk
	MarketChangesID    field.Int64  // 行情异动表 id (market_changes.market_changes_id)
	TradingSectionID   field.Int64  // 交易板块表 id (trading_section.id)
	TradingSectionName field.String // 交易板块名称(trading_section.name)
	CreatedAt          field.Time   // 创建时间
	UpdatedAt          field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (m marketChangesTradingSection) Table(newTableName string) *marketChangesTradingSection {
	m.marketChangesTradingSectionDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m marketChangesTradingSection) As(alias string) *marketChangesTradingSection {
	m.marketChangesTradingSectionDo.DO = *(m.marketChangesTradingSectionDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *marketChangesTradingSection) updateTableName(table string) *marketChangesTradingSection {
	m.ALL = field.NewAsterisk(table)
	m.MarketChangesID = field.NewInt64(table, "market_changes_id")
	m.TradingSectionID = field.NewInt64(table, "trading_section_id")
	m.TradingSectionName = field.NewString(table, "trading_section_name")
	m.CreatedAt = field.NewTime(table, "created_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *marketChangesTradingSection) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *marketChangesTradingSection) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 5)
	m.fieldMap["market_changes_id"] = m.MarketChangesID
	m.fieldMap["trading_section_id"] = m.TradingSectionID
	m.fieldMap["trading_section_name"] = m.TradingSectionName
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m marketChangesTradingSection) clone(db *gorm.DB) marketChangesTradingSection {
	m.marketChangesTradingSectionDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m marketChangesTradingSection) replaceDB(db *gorm.DB) marketChangesTradingSection {
	m.marketChangesTradingSectionDo.ReplaceDB(db)
	return m
}

type marketChangesTradingSectionDo struct{ gen.DO }

type IMarketChangesTradingSectionDo interface {
	gen.SubQuery
	Debug() IMarketChangesTradingSectionDo
	WithContext(ctx context.Context) IMarketChangesTradingSectionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMarketChangesTradingSectionDo
	WriteDB() IMarketChangesTradingSectionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMarketChangesTradingSectionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMarketChangesTradingSectionDo
	Not(conds ...gen.Condition) IMarketChangesTradingSectionDo
	Or(conds ...gen.Condition) IMarketChangesTradingSectionDo
	Select(conds ...field.Expr) IMarketChangesTradingSectionDo
	Where(conds ...gen.Condition) IMarketChangesTradingSectionDo
	Order(conds ...field.Expr) IMarketChangesTradingSectionDo
	Distinct(cols ...field.Expr) IMarketChangesTradingSectionDo
	Omit(cols ...field.Expr) IMarketChangesTradingSectionDo
	Join(table schema.Tabler, on ...field.Expr) IMarketChangesTradingSectionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMarketChangesTradingSectionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMarketChangesTradingSectionDo
	Group(cols ...field.Expr) IMarketChangesTradingSectionDo
	Having(conds ...gen.Condition) IMarketChangesTradingSectionDo
	Limit(limit int) IMarketChangesTradingSectionDo
	Offset(offset int) IMarketChangesTradingSectionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMarketChangesTradingSectionDo
	Unscoped() IMarketChangesTradingSectionDo
	Create(values ...*model.MarketChangesTradingSection) error
	CreateInBatches(values []*model.MarketChangesTradingSection, batchSize int) error
	Save(values ...*model.MarketChangesTradingSection) error
	First() (*model.MarketChangesTradingSection, error)
	Take() (*model.MarketChangesTradingSection, error)
	Last() (*model.MarketChangesTradingSection, error)
	Find() ([]*model.MarketChangesTradingSection, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MarketChangesTradingSection, err error)
	FindInBatches(result *[]*model.MarketChangesTradingSection, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MarketChangesTradingSection) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMarketChangesTradingSectionDo
	Assign(attrs ...field.AssignExpr) IMarketChangesTradingSectionDo
	Joins(fields ...field.RelationField) IMarketChangesTradingSectionDo
	Preload(fields ...field.RelationField) IMarketChangesTradingSectionDo
	FirstOrInit() (*model.MarketChangesTradingSection, error)
	FirstOrCreate() (*model.MarketChangesTradingSection, error)
	FindByPage(offset int, limit int) (result []*model.MarketChangesTradingSection, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMarketChangesTradingSectionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m marketChangesTradingSectionDo) Debug() IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Debug())
}

func (m marketChangesTradingSectionDo) WithContext(ctx context.Context) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m marketChangesTradingSectionDo) ReadDB() IMarketChangesTradingSectionDo {
	return m.Clauses(dbresolver.Read)
}

func (m marketChangesTradingSectionDo) WriteDB() IMarketChangesTradingSectionDo {
	return m.Clauses(dbresolver.Write)
}

func (m marketChangesTradingSectionDo) Session(config *gorm.Session) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Session(config))
}

func (m marketChangesTradingSectionDo) Clauses(conds ...clause.Expression) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m marketChangesTradingSectionDo) Returning(value interface{}, columns ...string) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m marketChangesTradingSectionDo) Not(conds ...gen.Condition) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m marketChangesTradingSectionDo) Or(conds ...gen.Condition) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m marketChangesTradingSectionDo) Select(conds ...field.Expr) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m marketChangesTradingSectionDo) Where(conds ...gen.Condition) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m marketChangesTradingSectionDo) Order(conds ...field.Expr) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m marketChangesTradingSectionDo) Distinct(cols ...field.Expr) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m marketChangesTradingSectionDo) Omit(cols ...field.Expr) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m marketChangesTradingSectionDo) Join(table schema.Tabler, on ...field.Expr) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m marketChangesTradingSectionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m marketChangesTradingSectionDo) RightJoin(table schema.Tabler, on ...field.Expr) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m marketChangesTradingSectionDo) Group(cols ...field.Expr) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m marketChangesTradingSectionDo) Having(conds ...gen.Condition) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m marketChangesTradingSectionDo) Limit(limit int) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m marketChangesTradingSectionDo) Offset(offset int) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m marketChangesTradingSectionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m marketChangesTradingSectionDo) Unscoped() IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Unscoped())
}

func (m marketChangesTradingSectionDo) Create(values ...*model.MarketChangesTradingSection) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m marketChangesTradingSectionDo) CreateInBatches(values []*model.MarketChangesTradingSection, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m marketChangesTradingSectionDo) Save(values ...*model.MarketChangesTradingSection) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m marketChangesTradingSectionDo) First() (*model.MarketChangesTradingSection, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesTradingSection), nil
	}
}

func (m marketChangesTradingSectionDo) Take() (*model.MarketChangesTradingSection, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesTradingSection), nil
	}
}

func (m marketChangesTradingSectionDo) Last() (*model.MarketChangesTradingSection, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesTradingSection), nil
	}
}

func (m marketChangesTradingSectionDo) Find() ([]*model.MarketChangesTradingSection, error) {
	result, err := m.DO.Find()
	return result.([]*model.MarketChangesTradingSection), err
}

func (m marketChangesTradingSectionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MarketChangesTradingSection, err error) {
	buf := make([]*model.MarketChangesTradingSection, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m marketChangesTradingSectionDo) FindInBatches(result *[]*model.MarketChangesTradingSection, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m marketChangesTradingSectionDo) Attrs(attrs ...field.AssignExpr) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m marketChangesTradingSectionDo) Assign(attrs ...field.AssignExpr) IMarketChangesTradingSectionDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m marketChangesTradingSectionDo) Joins(fields ...field.RelationField) IMarketChangesTradingSectionDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m marketChangesTradingSectionDo) Preload(fields ...field.RelationField) IMarketChangesTradingSectionDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m marketChangesTradingSectionDo) FirstOrInit() (*model.MarketChangesTradingSection, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesTradingSection), nil
	}
}

func (m marketChangesTradingSectionDo) FirstOrCreate() (*model.MarketChangesTradingSection, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesTradingSection), nil
	}
}

func (m marketChangesTradingSectionDo) FindByPage(offset int, limit int) (result []*model.MarketChangesTradingSection, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m marketChangesTradingSectionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m marketChangesTradingSectionDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m marketChangesTradingSectionDo) Delete(models ...*model.MarketChangesTradingSection) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *marketChangesTradingSectionDo) withDO(do gen.Dao) *marketChangesTradingSectionDo {
	m.DO = *do.(*gen.DO)
	return m
}
