// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/trading_section/dal/model"
)

func newAnnTradingSection(db *gorm.DB, opts ...gen.DOOption) annTradingSection {
	_annTradingSection := annTradingSection{}

	_annTradingSection.annTradingSectionDo.UseDB(db, opts...)
	_annTradingSection.annTradingSectionDo.UseModel(&model.AnnTradingSection{})

	tableName := _annTradingSection.annTradingSectionDo.TableName()
	_annTradingSection.ALL = field.NewAsterisk(tableName)
	_annTradingSection.AnnID = field.NewInt64(tableName, "ann_id")
	_annTradingSection.TradingSectionID = field.NewInt64(tableName, "trading_section_id")
	_annTradingSection.TradingSectionName = field.NewString(tableName, "trading_section_name")
	_annTradingSection.CreatedAt = field.NewTime(tableName, "created_at")
	_annTradingSection.UpdatedAt = field.NewTime(tableName, "updated_at")

	_annTradingSection.fillFieldMap()

	return _annTradingSection
}

// annTradingSection 平台方公告-交易板块关联表
type annTradingSection struct {
	annTradingSectionDo

	ALL                field.Asterisk
	AnnID              field.Int64  // 平台方公告表 id (announcement.ann_id)
	TradingSectionID   field.Int64  // 交易板块表 id (trading_section.id)
	TradingSectionName field.String // 交易板块名称(trading_section.name)
	CreatedAt          field.Time   // 创建时间
	UpdatedAt          field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (a annTradingSection) Table(newTableName string) *annTradingSection {
	a.annTradingSectionDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a annTradingSection) As(alias string) *annTradingSection {
	a.annTradingSectionDo.DO = *(a.annTradingSectionDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *annTradingSection) updateTableName(table string) *annTradingSection {
	a.ALL = field.NewAsterisk(table)
	a.AnnID = field.NewInt64(table, "ann_id")
	a.TradingSectionID = field.NewInt64(table, "trading_section_id")
	a.TradingSectionName = field.NewString(table, "trading_section_name")
	a.CreatedAt = field.NewTime(table, "created_at")
	a.UpdatedAt = field.NewTime(table, "updated_at")

	a.fillFieldMap()

	return a
}

func (a *annTradingSection) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *annTradingSection) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 5)
	a.fieldMap["ann_id"] = a.AnnID
	a.fieldMap["trading_section_id"] = a.TradingSectionID
	a.fieldMap["trading_section_name"] = a.TradingSectionName
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
}

func (a annTradingSection) clone(db *gorm.DB) annTradingSection {
	a.annTradingSectionDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a annTradingSection) replaceDB(db *gorm.DB) annTradingSection {
	a.annTradingSectionDo.ReplaceDB(db)
	return a
}

type annTradingSectionDo struct{ gen.DO }

type IAnnTradingSectionDo interface {
	gen.SubQuery
	Debug() IAnnTradingSectionDo
	WithContext(ctx context.Context) IAnnTradingSectionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAnnTradingSectionDo
	WriteDB() IAnnTradingSectionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAnnTradingSectionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAnnTradingSectionDo
	Not(conds ...gen.Condition) IAnnTradingSectionDo
	Or(conds ...gen.Condition) IAnnTradingSectionDo
	Select(conds ...field.Expr) IAnnTradingSectionDo
	Where(conds ...gen.Condition) IAnnTradingSectionDo
	Order(conds ...field.Expr) IAnnTradingSectionDo
	Distinct(cols ...field.Expr) IAnnTradingSectionDo
	Omit(cols ...field.Expr) IAnnTradingSectionDo
	Join(table schema.Tabler, on ...field.Expr) IAnnTradingSectionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAnnTradingSectionDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAnnTradingSectionDo
	Group(cols ...field.Expr) IAnnTradingSectionDo
	Having(conds ...gen.Condition) IAnnTradingSectionDo
	Limit(limit int) IAnnTradingSectionDo
	Offset(offset int) IAnnTradingSectionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAnnTradingSectionDo
	Unscoped() IAnnTradingSectionDo
	Create(values ...*model.AnnTradingSection) error
	CreateInBatches(values []*model.AnnTradingSection, batchSize int) error
	Save(values ...*model.AnnTradingSection) error
	First() (*model.AnnTradingSection, error)
	Take() (*model.AnnTradingSection, error)
	Last() (*model.AnnTradingSection, error)
	Find() ([]*model.AnnTradingSection, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AnnTradingSection, err error)
	FindInBatches(result *[]*model.AnnTradingSection, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AnnTradingSection) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAnnTradingSectionDo
	Assign(attrs ...field.AssignExpr) IAnnTradingSectionDo
	Joins(fields ...field.RelationField) IAnnTradingSectionDo
	Preload(fields ...field.RelationField) IAnnTradingSectionDo
	FirstOrInit() (*model.AnnTradingSection, error)
	FirstOrCreate() (*model.AnnTradingSection, error)
	FindByPage(offset int, limit int) (result []*model.AnnTradingSection, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAnnTradingSectionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a annTradingSectionDo) Debug() IAnnTradingSectionDo {
	return a.withDO(a.DO.Debug())
}

func (a annTradingSectionDo) WithContext(ctx context.Context) IAnnTradingSectionDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a annTradingSectionDo) ReadDB() IAnnTradingSectionDo {
	return a.Clauses(dbresolver.Read)
}

func (a annTradingSectionDo) WriteDB() IAnnTradingSectionDo {
	return a.Clauses(dbresolver.Write)
}

func (a annTradingSectionDo) Session(config *gorm.Session) IAnnTradingSectionDo {
	return a.withDO(a.DO.Session(config))
}

func (a annTradingSectionDo) Clauses(conds ...clause.Expression) IAnnTradingSectionDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a annTradingSectionDo) Returning(value interface{}, columns ...string) IAnnTradingSectionDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a annTradingSectionDo) Not(conds ...gen.Condition) IAnnTradingSectionDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a annTradingSectionDo) Or(conds ...gen.Condition) IAnnTradingSectionDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a annTradingSectionDo) Select(conds ...field.Expr) IAnnTradingSectionDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a annTradingSectionDo) Where(conds ...gen.Condition) IAnnTradingSectionDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a annTradingSectionDo) Order(conds ...field.Expr) IAnnTradingSectionDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a annTradingSectionDo) Distinct(cols ...field.Expr) IAnnTradingSectionDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a annTradingSectionDo) Omit(cols ...field.Expr) IAnnTradingSectionDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a annTradingSectionDo) Join(table schema.Tabler, on ...field.Expr) IAnnTradingSectionDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a annTradingSectionDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAnnTradingSectionDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a annTradingSectionDo) RightJoin(table schema.Tabler, on ...field.Expr) IAnnTradingSectionDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a annTradingSectionDo) Group(cols ...field.Expr) IAnnTradingSectionDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a annTradingSectionDo) Having(conds ...gen.Condition) IAnnTradingSectionDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a annTradingSectionDo) Limit(limit int) IAnnTradingSectionDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a annTradingSectionDo) Offset(offset int) IAnnTradingSectionDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a annTradingSectionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAnnTradingSectionDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a annTradingSectionDo) Unscoped() IAnnTradingSectionDo {
	return a.withDO(a.DO.Unscoped())
}

func (a annTradingSectionDo) Create(values ...*model.AnnTradingSection) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a annTradingSectionDo) CreateInBatches(values []*model.AnnTradingSection, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a annTradingSectionDo) Save(values ...*model.AnnTradingSection) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a annTradingSectionDo) First() (*model.AnnTradingSection, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AnnTradingSection), nil
	}
}

func (a annTradingSectionDo) Take() (*model.AnnTradingSection, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AnnTradingSection), nil
	}
}

func (a annTradingSectionDo) Last() (*model.AnnTradingSection, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AnnTradingSection), nil
	}
}

func (a annTradingSectionDo) Find() ([]*model.AnnTradingSection, error) {
	result, err := a.DO.Find()
	return result.([]*model.AnnTradingSection), err
}

func (a annTradingSectionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AnnTradingSection, err error) {
	buf := make([]*model.AnnTradingSection, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a annTradingSectionDo) FindInBatches(result *[]*model.AnnTradingSection, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a annTradingSectionDo) Attrs(attrs ...field.AssignExpr) IAnnTradingSectionDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a annTradingSectionDo) Assign(attrs ...field.AssignExpr) IAnnTradingSectionDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a annTradingSectionDo) Joins(fields ...field.RelationField) IAnnTradingSectionDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a annTradingSectionDo) Preload(fields ...field.RelationField) IAnnTradingSectionDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a annTradingSectionDo) FirstOrInit() (*model.AnnTradingSection, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AnnTradingSection), nil
	}
}

func (a annTradingSectionDo) FirstOrCreate() (*model.AnnTradingSection, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AnnTradingSection), nil
	}
}

func (a annTradingSectionDo) FindByPage(offset int, limit int) (result []*model.AnnTradingSection, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a annTradingSectionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a annTradingSectionDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a annTradingSectionDo) Delete(models ...*model.AnnTradingSection) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *annTradingSectionDo) withDO(do gen.Dao) *annTradingSectionDo {
	a.DO = *do.(*gen.DO)
	return a
}
