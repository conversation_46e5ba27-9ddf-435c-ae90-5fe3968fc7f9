// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/trading_section/dal/model"
)

func newTradingSectionItem(db *gorm.DB, opts ...gen.DOOption) tradingSectionItem {
	_tradingSectionItem := tradingSectionItem{}

	_tradingSectionItem.tradingSectionItemDo.UseDB(db, opts...)
	_tradingSectionItem.tradingSectionItemDo.UseModel(&model.TradingSectionItem{})

	tableName := _tradingSectionItem.tradingSectionItemDo.TableName()
	_tradingSectionItem.ALL = field.NewAsterisk(tableName)
	_tradingSectionItem.TradingSectionID = field.NewInt64(tableName, "trading_section_id")
	_tradingSectionItem.ItemID = field.NewString(tableName, "item_id")
	_tradingSectionItem.ItemName = field.NewString(tableName, "item_name")
	_tradingSectionItem.ImageURL = field.NewString(tableName, "image_url")
	_tradingSectionItem.CreatedAt = field.NewTime(tableName, "created_at")
	_tradingSectionItem.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tradingSectionItem.fillFieldMap()

	return _tradingSectionItem
}

// tradingSectionItem 交易板块-商品关联表
type tradingSectionItem struct {
	tradingSectionItemDo

	ALL              field.Asterisk
	TradingSectionID field.Int64  // 板块分区表 id (trading_section.id)
	ItemID           field.String // 商品 id (issue_item.item_id)
	ItemName         field.String // 商品名称(issue_item.item_name)
	ImageURL         field.String // 商品图片(issue_item.image_url)
	CreatedAt        field.Time   // 创建时间
	UpdatedAt        field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (t tradingSectionItem) Table(newTableName string) *tradingSectionItem {
	t.tradingSectionItemDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tradingSectionItem) As(alias string) *tradingSectionItem {
	t.tradingSectionItemDo.DO = *(t.tradingSectionItemDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tradingSectionItem) updateTableName(table string) *tradingSectionItem {
	t.ALL = field.NewAsterisk(table)
	t.TradingSectionID = field.NewInt64(table, "trading_section_id")
	t.ItemID = field.NewString(table, "item_id")
	t.ItemName = field.NewString(table, "item_name")
	t.ImageURL = field.NewString(table, "image_url")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tradingSectionItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tradingSectionItem) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 6)
	t.fieldMap["trading_section_id"] = t.TradingSectionID
	t.fieldMap["item_id"] = t.ItemID
	t.fieldMap["item_name"] = t.ItemName
	t.fieldMap["image_url"] = t.ImageURL
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tradingSectionItem) clone(db *gorm.DB) tradingSectionItem {
	t.tradingSectionItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tradingSectionItem) replaceDB(db *gorm.DB) tradingSectionItem {
	t.tradingSectionItemDo.ReplaceDB(db)
	return t
}

type tradingSectionItemDo struct{ gen.DO }

type ITradingSectionItemDo interface {
	gen.SubQuery
	Debug() ITradingSectionItemDo
	WithContext(ctx context.Context) ITradingSectionItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ITradingSectionItemDo
	WriteDB() ITradingSectionItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ITradingSectionItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ITradingSectionItemDo
	Not(conds ...gen.Condition) ITradingSectionItemDo
	Or(conds ...gen.Condition) ITradingSectionItemDo
	Select(conds ...field.Expr) ITradingSectionItemDo
	Where(conds ...gen.Condition) ITradingSectionItemDo
	Order(conds ...field.Expr) ITradingSectionItemDo
	Distinct(cols ...field.Expr) ITradingSectionItemDo
	Omit(cols ...field.Expr) ITradingSectionItemDo
	Join(table schema.Tabler, on ...field.Expr) ITradingSectionItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ITradingSectionItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) ITradingSectionItemDo
	Group(cols ...field.Expr) ITradingSectionItemDo
	Having(conds ...gen.Condition) ITradingSectionItemDo
	Limit(limit int) ITradingSectionItemDo
	Offset(offset int) ITradingSectionItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ITradingSectionItemDo
	Unscoped() ITradingSectionItemDo
	Create(values ...*model.TradingSectionItem) error
	CreateInBatches(values []*model.TradingSectionItem, batchSize int) error
	Save(values ...*model.TradingSectionItem) error
	First() (*model.TradingSectionItem, error)
	Take() (*model.TradingSectionItem, error)
	Last() (*model.TradingSectionItem, error)
	Find() ([]*model.TradingSectionItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TradingSectionItem, err error)
	FindInBatches(result *[]*model.TradingSectionItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.TradingSectionItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ITradingSectionItemDo
	Assign(attrs ...field.AssignExpr) ITradingSectionItemDo
	Joins(fields ...field.RelationField) ITradingSectionItemDo
	Preload(fields ...field.RelationField) ITradingSectionItemDo
	FirstOrInit() (*model.TradingSectionItem, error)
	FirstOrCreate() (*model.TradingSectionItem, error)
	FindByPage(offset int, limit int) (result []*model.TradingSectionItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ITradingSectionItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t tradingSectionItemDo) Debug() ITradingSectionItemDo {
	return t.withDO(t.DO.Debug())
}

func (t tradingSectionItemDo) WithContext(ctx context.Context) ITradingSectionItemDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tradingSectionItemDo) ReadDB() ITradingSectionItemDo {
	return t.Clauses(dbresolver.Read)
}

func (t tradingSectionItemDo) WriteDB() ITradingSectionItemDo {
	return t.Clauses(dbresolver.Write)
}

func (t tradingSectionItemDo) Session(config *gorm.Session) ITradingSectionItemDo {
	return t.withDO(t.DO.Session(config))
}

func (t tradingSectionItemDo) Clauses(conds ...clause.Expression) ITradingSectionItemDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tradingSectionItemDo) Returning(value interface{}, columns ...string) ITradingSectionItemDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tradingSectionItemDo) Not(conds ...gen.Condition) ITradingSectionItemDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tradingSectionItemDo) Or(conds ...gen.Condition) ITradingSectionItemDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tradingSectionItemDo) Select(conds ...field.Expr) ITradingSectionItemDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tradingSectionItemDo) Where(conds ...gen.Condition) ITradingSectionItemDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tradingSectionItemDo) Order(conds ...field.Expr) ITradingSectionItemDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tradingSectionItemDo) Distinct(cols ...field.Expr) ITradingSectionItemDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tradingSectionItemDo) Omit(cols ...field.Expr) ITradingSectionItemDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tradingSectionItemDo) Join(table schema.Tabler, on ...field.Expr) ITradingSectionItemDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tradingSectionItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) ITradingSectionItemDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tradingSectionItemDo) RightJoin(table schema.Tabler, on ...field.Expr) ITradingSectionItemDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tradingSectionItemDo) Group(cols ...field.Expr) ITradingSectionItemDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tradingSectionItemDo) Having(conds ...gen.Condition) ITradingSectionItemDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tradingSectionItemDo) Limit(limit int) ITradingSectionItemDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tradingSectionItemDo) Offset(offset int) ITradingSectionItemDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tradingSectionItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ITradingSectionItemDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tradingSectionItemDo) Unscoped() ITradingSectionItemDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tradingSectionItemDo) Create(values ...*model.TradingSectionItem) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tradingSectionItemDo) CreateInBatches(values []*model.TradingSectionItem, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tradingSectionItemDo) Save(values ...*model.TradingSectionItem) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tradingSectionItemDo) First() (*model.TradingSectionItem, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSectionItem), nil
	}
}

func (t tradingSectionItemDo) Take() (*model.TradingSectionItem, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSectionItem), nil
	}
}

func (t tradingSectionItemDo) Last() (*model.TradingSectionItem, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSectionItem), nil
	}
}

func (t tradingSectionItemDo) Find() ([]*model.TradingSectionItem, error) {
	result, err := t.DO.Find()
	return result.([]*model.TradingSectionItem), err
}

func (t tradingSectionItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TradingSectionItem, err error) {
	buf := make([]*model.TradingSectionItem, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tradingSectionItemDo) FindInBatches(result *[]*model.TradingSectionItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tradingSectionItemDo) Attrs(attrs ...field.AssignExpr) ITradingSectionItemDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tradingSectionItemDo) Assign(attrs ...field.AssignExpr) ITradingSectionItemDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tradingSectionItemDo) Joins(fields ...field.RelationField) ITradingSectionItemDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tradingSectionItemDo) Preload(fields ...field.RelationField) ITradingSectionItemDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tradingSectionItemDo) FirstOrInit() (*model.TradingSectionItem, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSectionItem), nil
	}
}

func (t tradingSectionItemDo) FirstOrCreate() (*model.TradingSectionItem, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSectionItem), nil
	}
}

func (t tradingSectionItemDo) FindByPage(offset int, limit int) (result []*model.TradingSectionItem, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tradingSectionItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tradingSectionItemDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tradingSectionItemDo) Delete(models ...*model.TradingSectionItem) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tradingSectionItemDo) withDO(do gen.Dao) *tradingSectionItemDo {
	t.DO = *do.(*gen.DO)
	return t
}
