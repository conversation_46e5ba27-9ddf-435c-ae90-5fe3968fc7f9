// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                           = new(Query)
	AnnTradingSection           *annTradingSection
	MarketChangesTradingSection *marketChangesTradingSection
	OperAnnTradingSection       *operAnnTradingSection
	TradingSection              *tradingSection
	TradingSectionDailyStat     *tradingSectionDailyStat
	TradingSectionItem          *tradingSectionItem
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	AnnTradingSection = &Q.AnnTradingSection
	MarketChangesTradingSection = &Q.MarketChangesTradingSection
	OperAnnTradingSection = &Q.OperAnnTradingSection
	TradingSection = &Q.TradingSection
	TradingSectionDailyStat = &Q.TradingSectionDailyStat
	TradingSectionItem = &Q.TradingSectionItem
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                          db,
		AnnTradingSection:           newAnnTradingSection(db, opts...),
		MarketChangesTradingSection: newMarketChangesTradingSection(db, opts...),
		OperAnnTradingSection:       newOperAnnTradingSection(db, opts...),
		TradingSection:              newTradingSection(db, opts...),
		TradingSectionDailyStat:     newTradingSectionDailyStat(db, opts...),
		TradingSectionItem:          newTradingSectionItem(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	AnnTradingSection           annTradingSection
	MarketChangesTradingSection marketChangesTradingSection
	OperAnnTradingSection       operAnnTradingSection
	TradingSection              tradingSection
	TradingSectionDailyStat     tradingSectionDailyStat
	TradingSectionItem          tradingSectionItem
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                          db,
		AnnTradingSection:           q.AnnTradingSection.clone(db),
		MarketChangesTradingSection: q.MarketChangesTradingSection.clone(db),
		OperAnnTradingSection:       q.OperAnnTradingSection.clone(db),
		TradingSection:              q.TradingSection.clone(db),
		TradingSectionDailyStat:     q.TradingSectionDailyStat.clone(db),
		TradingSectionItem:          q.TradingSectionItem.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                          db,
		AnnTradingSection:           q.AnnTradingSection.replaceDB(db),
		MarketChangesTradingSection: q.MarketChangesTradingSection.replaceDB(db),
		OperAnnTradingSection:       q.OperAnnTradingSection.replaceDB(db),
		TradingSection:              q.TradingSection.replaceDB(db),
		TradingSectionDailyStat:     q.TradingSectionDailyStat.replaceDB(db),
		TradingSectionItem:          q.TradingSectionItem.replaceDB(db),
	}
}

type queryCtx struct {
	AnnTradingSection           IAnnTradingSectionDo
	MarketChangesTradingSection IMarketChangesTradingSectionDo
	OperAnnTradingSection       IOperAnnTradingSectionDo
	TradingSection              ITradingSectionDo
	TradingSectionDailyStat     ITradingSectionDailyStatDo
	TradingSectionItem          ITradingSectionItemDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		AnnTradingSection:           q.AnnTradingSection.WithContext(ctx),
		MarketChangesTradingSection: q.MarketChangesTradingSection.WithContext(ctx),
		OperAnnTradingSection:       q.OperAnnTradingSection.WithContext(ctx),
		TradingSection:              q.TradingSection.WithContext(ctx),
		TradingSectionDailyStat:     q.TradingSectionDailyStat.WithContext(ctx),
		TradingSectionItem:          q.TradingSectionItem.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
