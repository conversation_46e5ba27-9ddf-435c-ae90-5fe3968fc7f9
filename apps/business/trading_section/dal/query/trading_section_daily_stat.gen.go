// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/trading_section/dal/model"
)

func newTradingSectionDailyStat(db *gorm.DB, opts ...gen.DOOption) tradingSectionDailyStat {
	_tradingSectionDailyStat := tradingSectionDailyStat{}

	_tradingSectionDailyStat.tradingSectionDailyStatDo.UseDB(db, opts...)
	_tradingSectionDailyStat.tradingSectionDailyStatDo.UseModel(&model.TradingSectionDailyStat{})

	tableName := _tradingSectionDailyStat.tradingSectionDailyStatDo.TableName()
	_tradingSectionDailyStat.ALL = field.NewAsterisk(tableName)
	_tradingSectionDailyStat.ID = field.NewInt64(tableName, "id")
	_tradingSectionDailyStat.TradingSectionID = field.NewInt64(tableName, "trading_section_id")
	_tradingSectionDailyStat.TradingDay = field.NewTime(tableName, "trading_day")
	_tradingSectionDailyStat.ClosePriceRate = field.NewFloat32(tableName, "close_price_rate")
	_tradingSectionDailyStat.MarketAmount = field.NewInt64(tableName, "market_amount")
	_tradingSectionDailyStat.MarketAmountRatio = field.NewFloat32(tableName, "market_amount_ratio")
	_tradingSectionDailyStat.TransactionAmount = field.NewInt64(tableName, "transaction_amount")
	_tradingSectionDailyStat.TransactionAmountRatio = field.NewFloat32(tableName, "transaction_amount_ratio")
	_tradingSectionDailyStat.ItemsSnapshot = field.NewField(tableName, "items_snapshot")
	_tradingSectionDailyStat.CreatedAt = field.NewTime(tableName, "created_at")
	_tradingSectionDailyStat.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tradingSectionDailyStat.fillFieldMap()

	return _tradingSectionDailyStat
}

// tradingSectionDailyStat 交易板块每日数据统计表
type tradingSectionDailyStat struct {
	tradingSectionDailyStatDo

	ALL                    field.Asterisk
	ID                     field.Int64   // 主键 id
	TradingSectionID       field.Int64   // 交易板块表 id (trading_section.id)
	TradingDay             field.Time    // 交易日
	ClosePriceRate         field.Float32 // 当天的收盘价涨跌幅
	MarketAmount           field.Int64   // 当天所有商品的总市值(单位:分)
	MarketAmountRatio      field.Float32 // 当天的市值百分比
	TransactionAmount      field.Int64   // 当天所有商品的总成交额(单位:分)
	TransactionAmountRatio field.Float32 // 当天的成交额百分比
	ItemsSnapshot          field.Field   // 商品数据快照
	CreatedAt              field.Time    // 创建时间
	UpdatedAt              field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (t tradingSectionDailyStat) Table(newTableName string) *tradingSectionDailyStat {
	t.tradingSectionDailyStatDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tradingSectionDailyStat) As(alias string) *tradingSectionDailyStat {
	t.tradingSectionDailyStatDo.DO = *(t.tradingSectionDailyStatDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tradingSectionDailyStat) updateTableName(table string) *tradingSectionDailyStat {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt64(table, "id")
	t.TradingSectionID = field.NewInt64(table, "trading_section_id")
	t.TradingDay = field.NewTime(table, "trading_day")
	t.ClosePriceRate = field.NewFloat32(table, "close_price_rate")
	t.MarketAmount = field.NewInt64(table, "market_amount")
	t.MarketAmountRatio = field.NewFloat32(table, "market_amount_ratio")
	t.TransactionAmount = field.NewInt64(table, "transaction_amount")
	t.TransactionAmountRatio = field.NewFloat32(table, "transaction_amount_ratio")
	t.ItemsSnapshot = field.NewField(table, "items_snapshot")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tradingSectionDailyStat) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tradingSectionDailyStat) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 11)
	t.fieldMap["id"] = t.ID
	t.fieldMap["trading_section_id"] = t.TradingSectionID
	t.fieldMap["trading_day"] = t.TradingDay
	t.fieldMap["close_price_rate"] = t.ClosePriceRate
	t.fieldMap["market_amount"] = t.MarketAmount
	t.fieldMap["market_amount_ratio"] = t.MarketAmountRatio
	t.fieldMap["transaction_amount"] = t.TransactionAmount
	t.fieldMap["transaction_amount_ratio"] = t.TransactionAmountRatio
	t.fieldMap["items_snapshot"] = t.ItemsSnapshot
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tradingSectionDailyStat) clone(db *gorm.DB) tradingSectionDailyStat {
	t.tradingSectionDailyStatDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tradingSectionDailyStat) replaceDB(db *gorm.DB) tradingSectionDailyStat {
	t.tradingSectionDailyStatDo.ReplaceDB(db)
	return t
}

type tradingSectionDailyStatDo struct{ gen.DO }

type ITradingSectionDailyStatDo interface {
	gen.SubQuery
	Debug() ITradingSectionDailyStatDo
	WithContext(ctx context.Context) ITradingSectionDailyStatDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ITradingSectionDailyStatDo
	WriteDB() ITradingSectionDailyStatDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ITradingSectionDailyStatDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ITradingSectionDailyStatDo
	Not(conds ...gen.Condition) ITradingSectionDailyStatDo
	Or(conds ...gen.Condition) ITradingSectionDailyStatDo
	Select(conds ...field.Expr) ITradingSectionDailyStatDo
	Where(conds ...gen.Condition) ITradingSectionDailyStatDo
	Order(conds ...field.Expr) ITradingSectionDailyStatDo
	Distinct(cols ...field.Expr) ITradingSectionDailyStatDo
	Omit(cols ...field.Expr) ITradingSectionDailyStatDo
	Join(table schema.Tabler, on ...field.Expr) ITradingSectionDailyStatDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ITradingSectionDailyStatDo
	RightJoin(table schema.Tabler, on ...field.Expr) ITradingSectionDailyStatDo
	Group(cols ...field.Expr) ITradingSectionDailyStatDo
	Having(conds ...gen.Condition) ITradingSectionDailyStatDo
	Limit(limit int) ITradingSectionDailyStatDo
	Offset(offset int) ITradingSectionDailyStatDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ITradingSectionDailyStatDo
	Unscoped() ITradingSectionDailyStatDo
	Create(values ...*model.TradingSectionDailyStat) error
	CreateInBatches(values []*model.TradingSectionDailyStat, batchSize int) error
	Save(values ...*model.TradingSectionDailyStat) error
	First() (*model.TradingSectionDailyStat, error)
	Take() (*model.TradingSectionDailyStat, error)
	Last() (*model.TradingSectionDailyStat, error)
	Find() ([]*model.TradingSectionDailyStat, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TradingSectionDailyStat, err error)
	FindInBatches(result *[]*model.TradingSectionDailyStat, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.TradingSectionDailyStat) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ITradingSectionDailyStatDo
	Assign(attrs ...field.AssignExpr) ITradingSectionDailyStatDo
	Joins(fields ...field.RelationField) ITradingSectionDailyStatDo
	Preload(fields ...field.RelationField) ITradingSectionDailyStatDo
	FirstOrInit() (*model.TradingSectionDailyStat, error)
	FirstOrCreate() (*model.TradingSectionDailyStat, error)
	FindByPage(offset int, limit int) (result []*model.TradingSectionDailyStat, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ITradingSectionDailyStatDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t tradingSectionDailyStatDo) Debug() ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Debug())
}

func (t tradingSectionDailyStatDo) WithContext(ctx context.Context) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tradingSectionDailyStatDo) ReadDB() ITradingSectionDailyStatDo {
	return t.Clauses(dbresolver.Read)
}

func (t tradingSectionDailyStatDo) WriteDB() ITradingSectionDailyStatDo {
	return t.Clauses(dbresolver.Write)
}

func (t tradingSectionDailyStatDo) Session(config *gorm.Session) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Session(config))
}

func (t tradingSectionDailyStatDo) Clauses(conds ...clause.Expression) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tradingSectionDailyStatDo) Returning(value interface{}, columns ...string) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tradingSectionDailyStatDo) Not(conds ...gen.Condition) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tradingSectionDailyStatDo) Or(conds ...gen.Condition) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tradingSectionDailyStatDo) Select(conds ...field.Expr) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tradingSectionDailyStatDo) Where(conds ...gen.Condition) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tradingSectionDailyStatDo) Order(conds ...field.Expr) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tradingSectionDailyStatDo) Distinct(cols ...field.Expr) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tradingSectionDailyStatDo) Omit(cols ...field.Expr) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tradingSectionDailyStatDo) Join(table schema.Tabler, on ...field.Expr) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tradingSectionDailyStatDo) LeftJoin(table schema.Tabler, on ...field.Expr) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tradingSectionDailyStatDo) RightJoin(table schema.Tabler, on ...field.Expr) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tradingSectionDailyStatDo) Group(cols ...field.Expr) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tradingSectionDailyStatDo) Having(conds ...gen.Condition) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tradingSectionDailyStatDo) Limit(limit int) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tradingSectionDailyStatDo) Offset(offset int) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tradingSectionDailyStatDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tradingSectionDailyStatDo) Unscoped() ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tradingSectionDailyStatDo) Create(values ...*model.TradingSectionDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tradingSectionDailyStatDo) CreateInBatches(values []*model.TradingSectionDailyStat, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tradingSectionDailyStatDo) Save(values ...*model.TradingSectionDailyStat) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tradingSectionDailyStatDo) First() (*model.TradingSectionDailyStat, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSectionDailyStat), nil
	}
}

func (t tradingSectionDailyStatDo) Take() (*model.TradingSectionDailyStat, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSectionDailyStat), nil
	}
}

func (t tradingSectionDailyStatDo) Last() (*model.TradingSectionDailyStat, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSectionDailyStat), nil
	}
}

func (t tradingSectionDailyStatDo) Find() ([]*model.TradingSectionDailyStat, error) {
	result, err := t.DO.Find()
	return result.([]*model.TradingSectionDailyStat), err
}

func (t tradingSectionDailyStatDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TradingSectionDailyStat, err error) {
	buf := make([]*model.TradingSectionDailyStat, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tradingSectionDailyStatDo) FindInBatches(result *[]*model.TradingSectionDailyStat, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tradingSectionDailyStatDo) Attrs(attrs ...field.AssignExpr) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tradingSectionDailyStatDo) Assign(attrs ...field.AssignExpr) ITradingSectionDailyStatDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tradingSectionDailyStatDo) Joins(fields ...field.RelationField) ITradingSectionDailyStatDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tradingSectionDailyStatDo) Preload(fields ...field.RelationField) ITradingSectionDailyStatDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tradingSectionDailyStatDo) FirstOrInit() (*model.TradingSectionDailyStat, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSectionDailyStat), nil
	}
}

func (t tradingSectionDailyStatDo) FirstOrCreate() (*model.TradingSectionDailyStat, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSectionDailyStat), nil
	}
}

func (t tradingSectionDailyStatDo) FindByPage(offset int, limit int) (result []*model.TradingSectionDailyStat, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tradingSectionDailyStatDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tradingSectionDailyStatDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tradingSectionDailyStatDo) Delete(models ...*model.TradingSectionDailyStat) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tradingSectionDailyStatDo) withDO(do gen.Dao) *tradingSectionDailyStatDo {
	t.DO = *do.(*gen.DO)
	return t
}
