// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/trading_section/dal/model"
)

func newTradingSection(db *gorm.DB, opts ...gen.DOOption) tradingSection {
	_tradingSection := tradingSection{}

	_tradingSection.tradingSectionDo.UseDB(db, opts...)
	_tradingSection.tradingSectionDo.UseModel(&model.TradingSection{})

	tableName := _tradingSection.tradingSectionDo.TableName()
	_tradingSection.ALL = field.NewAsterisk(tableName)
	_tradingSection.ID = field.NewInt64(tableName, "id")
	_tradingSection.Name = field.NewString(tableName, "name")
	_tradingSection.Description = field.NewString(tableName, "description")
	_tradingSection.Priority = field.NewInt32(tableName, "priority")
	_tradingSection.Status = field.NewInt32(tableName, "status")
	_tradingSection.PriceChangeRate = field.NewFloat32(tableName, "price_change_rate")
	_tradingSection.TransactionAmount = field.NewInt64(tableName, "transaction_amount")
	_tradingSection.TransactionAmountRatio = field.NewFloat32(tableName, "transaction_amount_ratio")
	_tradingSection.MarketAmount = field.NewInt64(tableName, "market_amount")
	_tradingSection.MarketAmountRatio = field.NewFloat32(tableName, "market_amount_ratio")
	_tradingSection.StatDataUpdateTime = field.NewTime(tableName, "stat_data_update_time")
	_tradingSection.CreatedAt = field.NewTime(tableName, "created_at")
	_tradingSection.UpdatedAt = field.NewTime(tableName, "updated_at")

	_tradingSection.fillFieldMap()

	return _tradingSection
}

// tradingSection 交易板块表
type tradingSection struct {
	tradingSectionDo

	ALL                    field.Asterisk
	ID                     field.Int64   // 主键 id
	Name                   field.String  // 板块名称
	Description            field.String  // 板块介绍
	Priority               field.Int32   // 优先级，数值越大优先级越高
	Status                 field.Int32   // 状态(0:关闭，1:启用)
	PriceChangeRate        field.Float32 // 板块涨跌幅
	TransactionAmount      field.Int64   // 最新成交额(单位：分)
	TransactionAmountRatio field.Float32 // 成交额百分比
	MarketAmount           field.Int64   // 最新市值(单位：分)
	MarketAmountRatio      field.Float32 // 市值百分比
	StatDataUpdateTime     field.Time    // 统计数据更新时间
	CreatedAt              field.Time    // 创建时间
	UpdatedAt              field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (t tradingSection) Table(newTableName string) *tradingSection {
	t.tradingSectionDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t tradingSection) As(alias string) *tradingSection {
	t.tradingSectionDo.DO = *(t.tradingSectionDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *tradingSection) updateTableName(table string) *tradingSection {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt64(table, "id")
	t.Name = field.NewString(table, "name")
	t.Description = field.NewString(table, "description")
	t.Priority = field.NewInt32(table, "priority")
	t.Status = field.NewInt32(table, "status")
	t.PriceChangeRate = field.NewFloat32(table, "price_change_rate")
	t.TransactionAmount = field.NewInt64(table, "transaction_amount")
	t.TransactionAmountRatio = field.NewFloat32(table, "transaction_amount_ratio")
	t.MarketAmount = field.NewInt64(table, "market_amount")
	t.MarketAmountRatio = field.NewFloat32(table, "market_amount_ratio")
	t.StatDataUpdateTime = field.NewTime(table, "stat_data_update_time")
	t.CreatedAt = field.NewTime(table, "created_at")
	t.UpdatedAt = field.NewTime(table, "updated_at")

	t.fillFieldMap()

	return t
}

func (t *tradingSection) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *tradingSection) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 13)
	t.fieldMap["id"] = t.ID
	t.fieldMap["name"] = t.Name
	t.fieldMap["description"] = t.Description
	t.fieldMap["priority"] = t.Priority
	t.fieldMap["status"] = t.Status
	t.fieldMap["price_change_rate"] = t.PriceChangeRate
	t.fieldMap["transaction_amount"] = t.TransactionAmount
	t.fieldMap["transaction_amount_ratio"] = t.TransactionAmountRatio
	t.fieldMap["market_amount"] = t.MarketAmount
	t.fieldMap["market_amount_ratio"] = t.MarketAmountRatio
	t.fieldMap["stat_data_update_time"] = t.StatDataUpdateTime
	t.fieldMap["created_at"] = t.CreatedAt
	t.fieldMap["updated_at"] = t.UpdatedAt
}

func (t tradingSection) clone(db *gorm.DB) tradingSection {
	t.tradingSectionDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t tradingSection) replaceDB(db *gorm.DB) tradingSection {
	t.tradingSectionDo.ReplaceDB(db)
	return t
}

type tradingSectionDo struct{ gen.DO }

type ITradingSectionDo interface {
	gen.SubQuery
	Debug() ITradingSectionDo
	WithContext(ctx context.Context) ITradingSectionDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ITradingSectionDo
	WriteDB() ITradingSectionDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ITradingSectionDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ITradingSectionDo
	Not(conds ...gen.Condition) ITradingSectionDo
	Or(conds ...gen.Condition) ITradingSectionDo
	Select(conds ...field.Expr) ITradingSectionDo
	Where(conds ...gen.Condition) ITradingSectionDo
	Order(conds ...field.Expr) ITradingSectionDo
	Distinct(cols ...field.Expr) ITradingSectionDo
	Omit(cols ...field.Expr) ITradingSectionDo
	Join(table schema.Tabler, on ...field.Expr) ITradingSectionDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ITradingSectionDo
	RightJoin(table schema.Tabler, on ...field.Expr) ITradingSectionDo
	Group(cols ...field.Expr) ITradingSectionDo
	Having(conds ...gen.Condition) ITradingSectionDo
	Limit(limit int) ITradingSectionDo
	Offset(offset int) ITradingSectionDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ITradingSectionDo
	Unscoped() ITradingSectionDo
	Create(values ...*model.TradingSection) error
	CreateInBatches(values []*model.TradingSection, batchSize int) error
	Save(values ...*model.TradingSection) error
	First() (*model.TradingSection, error)
	Take() (*model.TradingSection, error)
	Last() (*model.TradingSection, error)
	Find() ([]*model.TradingSection, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TradingSection, err error)
	FindInBatches(result *[]*model.TradingSection, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.TradingSection) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ITradingSectionDo
	Assign(attrs ...field.AssignExpr) ITradingSectionDo
	Joins(fields ...field.RelationField) ITradingSectionDo
	Preload(fields ...field.RelationField) ITradingSectionDo
	FirstOrInit() (*model.TradingSection, error)
	FirstOrCreate() (*model.TradingSection, error)
	FindByPage(offset int, limit int) (result []*model.TradingSection, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ITradingSectionDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t tradingSectionDo) Debug() ITradingSectionDo {
	return t.withDO(t.DO.Debug())
}

func (t tradingSectionDo) WithContext(ctx context.Context) ITradingSectionDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t tradingSectionDo) ReadDB() ITradingSectionDo {
	return t.Clauses(dbresolver.Read)
}

func (t tradingSectionDo) WriteDB() ITradingSectionDo {
	return t.Clauses(dbresolver.Write)
}

func (t tradingSectionDo) Session(config *gorm.Session) ITradingSectionDo {
	return t.withDO(t.DO.Session(config))
}

func (t tradingSectionDo) Clauses(conds ...clause.Expression) ITradingSectionDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t tradingSectionDo) Returning(value interface{}, columns ...string) ITradingSectionDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t tradingSectionDo) Not(conds ...gen.Condition) ITradingSectionDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t tradingSectionDo) Or(conds ...gen.Condition) ITradingSectionDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t tradingSectionDo) Select(conds ...field.Expr) ITradingSectionDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t tradingSectionDo) Where(conds ...gen.Condition) ITradingSectionDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t tradingSectionDo) Order(conds ...field.Expr) ITradingSectionDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t tradingSectionDo) Distinct(cols ...field.Expr) ITradingSectionDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t tradingSectionDo) Omit(cols ...field.Expr) ITradingSectionDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t tradingSectionDo) Join(table schema.Tabler, on ...field.Expr) ITradingSectionDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t tradingSectionDo) LeftJoin(table schema.Tabler, on ...field.Expr) ITradingSectionDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t tradingSectionDo) RightJoin(table schema.Tabler, on ...field.Expr) ITradingSectionDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t tradingSectionDo) Group(cols ...field.Expr) ITradingSectionDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t tradingSectionDo) Having(conds ...gen.Condition) ITradingSectionDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t tradingSectionDo) Limit(limit int) ITradingSectionDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t tradingSectionDo) Offset(offset int) ITradingSectionDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t tradingSectionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ITradingSectionDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t tradingSectionDo) Unscoped() ITradingSectionDo {
	return t.withDO(t.DO.Unscoped())
}

func (t tradingSectionDo) Create(values ...*model.TradingSection) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t tradingSectionDo) CreateInBatches(values []*model.TradingSection, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t tradingSectionDo) Save(values ...*model.TradingSection) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t tradingSectionDo) First() (*model.TradingSection, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSection), nil
	}
}

func (t tradingSectionDo) Take() (*model.TradingSection, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSection), nil
	}
}

func (t tradingSectionDo) Last() (*model.TradingSection, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSection), nil
	}
}

func (t tradingSectionDo) Find() ([]*model.TradingSection, error) {
	result, err := t.DO.Find()
	return result.([]*model.TradingSection), err
}

func (t tradingSectionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.TradingSection, err error) {
	buf := make([]*model.TradingSection, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t tradingSectionDo) FindInBatches(result *[]*model.TradingSection, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t tradingSectionDo) Attrs(attrs ...field.AssignExpr) ITradingSectionDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t tradingSectionDo) Assign(attrs ...field.AssignExpr) ITradingSectionDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t tradingSectionDo) Joins(fields ...field.RelationField) ITradingSectionDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t tradingSectionDo) Preload(fields ...field.RelationField) ITradingSectionDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t tradingSectionDo) FirstOrInit() (*model.TradingSection, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSection), nil
	}
}

func (t tradingSectionDo) FirstOrCreate() (*model.TradingSection, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.TradingSection), nil
	}
}

func (t tradingSectionDo) FindByPage(offset int, limit int) (result []*model.TradingSection, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t tradingSectionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t tradingSectionDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t tradingSectionDo) Delete(models ...*model.TradingSection) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *tradingSectionDo) withDO(do gen.Dao) *tradingSectionDo {
	t.DO = *do.(*gen.DO)
	return t
}
