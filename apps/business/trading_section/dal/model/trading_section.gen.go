// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTradingSection = "trading_section"

// TradingSection 交易板块表
type TradingSection struct {
	ID                     int64      `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键 id" json:"id"`                    // 主键 id
	Name                   string     `gorm:"column:name;type:varchar(16);not null;comment:板块名称" json:"name"`                                          // 板块名称
	Description            string     `gorm:"column:description;type:varchar(256);comment:板块介绍" json:"description"`                                    // 板块介绍
	Priority               int32      `gorm:"column:priority;type:int;not null;comment:优先级，数值越大优先级越高" json:"priority"`                                 // 优先级，数值越大优先级越高
	Status                 int32      `gorm:"column:status;type:tinyint;not null;comment:状态(0:关闭，1:启用)" json:"status"`                                 // 状态(0:关闭，1:启用)
	PriceChangeRate        float32    `gorm:"column:price_change_rate;type:float;not null;comment:板块涨跌幅" json:"price_change_rate"`                     // 板块涨跌幅
	TransactionAmount      int64      `gorm:"column:transaction_amount;type:bigint unsigned;not null;comment:最新成交额(单位：分)" json:"transaction_amount"`   // 最新成交额(单位：分)
	TransactionAmountRatio float32    `gorm:"column:transaction_amount_ratio;type:float;not null;comment:成交额百分比" json:"transaction_amount_ratio"`      // 成交额百分比
	MarketAmount           int64      `gorm:"column:market_amount;type:bigint unsigned;not null;comment:最新市值(单位：分)" json:"market_amount"`              // 最新市值(单位：分)
	MarketAmountRatio      float32    `gorm:"column:market_amount_ratio;type:float;not null;comment:市值百分比" json:"market_amount_ratio"`                 // 市值百分比
	StatDataUpdateTime     *time.Time `gorm:"column:stat_data_update_time;type:datetime(3);comment:统计数据更新时间" json:"stat_data_update_time"`             // 统计数据更新时间
	CreatedAt              time.Time  `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt              time.Time  `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName TradingSection's table name
func (*TradingSection) TableName() string {
	return TableNameTradingSection
}
