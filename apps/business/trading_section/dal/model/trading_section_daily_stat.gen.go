// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
)

const TableNameTradingSectionDailyStat = "trading_section_daily_stat"

// TradingSectionDailyStat 交易板块每日数据统计表
type TradingSectionDailyStat struct {
	ID                     int64           `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键 id" json:"id"`                                    // 主键 id
	TradingSectionID       int64           `gorm:"column:trading_section_id;type:bigint unsigned;not null;comment:交易板块表 id (trading_section.id)" json:"trading_section_id"` // 交易板块表 id (trading_section.id)
	TradingDay             time.Time       `gorm:"column:trading_day;type:date;not null;comment:交易日" json:"trading_day"`                                                    // 交易日
	ClosePriceRate         float32         `gorm:"column:close_price_rate;type:float;not null;comment:当天的收盘价涨跌幅" json:"close_price_rate"`                                   // 当天的收盘价涨跌幅
	MarketAmount           int64           `gorm:"column:market_amount;type:bigint unsigned;not null;comment:当天所有商品的总市值(单位:分)" json:"market_amount"`                        // 当天所有商品的总市值(单位:分)
	MarketAmountRatio      float32         `gorm:"column:market_amount_ratio;type:float;not null;comment:当天的市值百分比" json:"market_amount_ratio"`                              // 当天的市值百分比
	TransactionAmount      int64           `gorm:"column:transaction_amount;type:bigint unsigned;not null;comment:当天所有商品的总成交额(单位:分)" json:"transaction_amount"`             // 当天所有商品的总成交额(单位:分)
	TransactionAmountRatio float32         `gorm:"column:transaction_amount_ratio;type:float;not null;comment:当天的成交额百分比" json:"transaction_amount_ratio"`                   // 当天的成交额百分比
	ItemsSnapshot          *datatypes.JSON `gorm:"column:items_snapshot;type:json;comment:商品数据快照" json:"items_snapshot"`                                                    // 商品数据快照
	CreatedAt              time.Time       `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`                 // 创建时间
	UpdatedAt              time.Time       `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`                 // 更新时间
}

// TableName TradingSectionDailyStat's table name
func (*TradingSectionDailyStat) TableName() string {
	return TableNameTradingSectionDailyStat
}
