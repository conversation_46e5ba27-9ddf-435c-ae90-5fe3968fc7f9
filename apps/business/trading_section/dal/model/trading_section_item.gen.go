// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameTradingSectionItem = "trading_section_item"

// TradingSectionItem 交易板块-商品关联表
type TradingSectionItem struct {
	TradingSectionID int64     `gorm:"column:trading_section_id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:板块分区表 id (trading_section.id)" json:"trading_section_id"` // 板块分区表 id (trading_section.id)
	ItemID           string    `gorm:"column:item_id;type:varchar(32);primaryKey;comment:商品 id (issue_item.item_id)" json:"item_id"`                                                 // 商品 id (issue_item.item_id)
	ItemName         string    `gorm:"column:item_name;type:varchar(256);not null;comment:商品名称(issue_item.item_name)" json:"item_name"`                                              // 商品名称(issue_item.item_name)
	ImageURL         string    `gorm:"column:image_url;type:varchar(256);not null;comment:商品图片(issue_item.image_url)" json:"image_url"`                                              // 商品图片(issue_item.image_url)
	CreatedAt        time.Time `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`                                      // 创建时间
	UpdatedAt        time.Time `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`                                      // 更新时间
}

// TableName TradingSectionItem's table name
func (*TradingSectionItem) TableName() string {
	return TableNameTradingSectionItem
}
