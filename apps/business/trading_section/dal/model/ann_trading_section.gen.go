// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameAnnTradingSection = "ann_trading_section"

// AnnTradingSection 平台方公告-交易板块关联表
type AnnTradingSection struct {
	AnnID              int64     `gorm:"column:ann_id;type:bigint unsigned;primaryKey;comment:平台方公告表 id (announcement.ann_id)" json:"ann_id"`                       // 平台方公告表 id (announcement.ann_id)
	TradingSectionID   int64     `gorm:"column:trading_section_id;type:bigint unsigned;primaryKey;comment:交易板块表 id (trading_section.id)" json:"trading_section_id"` // 交易板块表 id (trading_section.id)
	TradingSectionName string    `gorm:"column:trading_section_name;type:varchar(16);not null;comment:交易板块名称(trading_section.name)" json:"trading_section_name"`    // 交易板块名称(trading_section.name)
	CreatedAt          time.Time `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`                   // 创建时间
	UpdatedAt          time.Time `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`                   // 更新时间
}

// TableName AnnTradingSection's table name
func (*AnnTradingSection) TableName() string {
	return TableNameAnnTradingSection
}
