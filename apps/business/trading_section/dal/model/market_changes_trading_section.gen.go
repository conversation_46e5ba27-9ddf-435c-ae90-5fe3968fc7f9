// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameMarketChangesTradingSection = "market_changes_trading_section"

// MarketChangesTradingSection 行情异动-交易板块关联表
type MarketChangesTradingSection struct {
	MarketChangesID    int64     `gorm:"column:market_changes_id;type:bigint unsigned;primaryKey;comment:行情异动表 id (market_changes.market_changes_id)" json:"market_changes_id"` // 行情异动表 id (market_changes.market_changes_id)
	TradingSectionID   int64     `gorm:"column:trading_section_id;type:bigint unsigned;primaryKey;comment:交易板块表 id (trading_section.id)" json:"trading_section_id"`             // 交易板块表 id (trading_section.id)
	TradingSectionName string    `gorm:"column:trading_section_name;type:varchar(16);not null;comment:交易板块名称(trading_section.name)" json:"trading_section_name"`                // 交易板块名称(trading_section.name)
	CreatedAt          time.Time `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`                               // 创建时间
	UpdatedAt          time.Time `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`                               // 更新时间
}

// TableName MarketChangesTradingSection's table name
func (*MarketChangesTradingSection) TableName() string {
	return TableNameMarketChangesTradingSection
}
