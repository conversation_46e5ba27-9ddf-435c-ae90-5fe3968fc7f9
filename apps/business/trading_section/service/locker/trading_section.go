package locker

import (
	"fmt"
	"time"
)

// TradingSectionAction 板块分区相关行为枚举
type TradingSectionAction string

const (
	RefreshDailyStat TradingSectionAction = "refresh_daily_stat" // 刷新每日数据统计
)

type TradingSectionLock struct {
	ac  TradingSectionAction // 行为
	tag string               // 唯一标识
}

func (p *TradingSectionLock) GetCacheKey() string {
	return fmt.Sprintf("trading_section:%s:%s", p.ac, p.tag)
}

func (p *TradingSectionLock) LockTime() time.Duration {
	if p.ac == RefreshDailyStat {
		return time.Minute * 5
	}
	return time.Second * 10
}

func NewTradingSectionLock(tag string, ac TradingSectionAction) *TradingSectionLock {
	return &TradingSectionLock{tag: tag, ac: ac}
}
