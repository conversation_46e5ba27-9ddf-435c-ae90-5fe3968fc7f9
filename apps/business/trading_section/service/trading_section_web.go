package service

import (
	annmodel "app_service/apps/business/announcement/dal/model"
	annrepo "app_service/apps/business/announcement/repo"
	annlogic "app_service/apps/business/announcement/service/logic"
	mcmodel "app_service/apps/business/market_changes/dal/model"
	mcrenums "app_service/apps/business/market_changes/define/enums"
	mcrepo "app_service/apps/business/market_changes/repo"
	operannmodel "app_service/apps/business/operation_announcement/dal/model"
	operannrepo "app_service/apps/business/operation_announcement/repo"
	operannlogic "app_service/apps/business/operation_announcement/service/logic"
	trademodel "app_service/apps/business/trade/dal/model"
	tradeenums "app_service/apps/business/trade/define/enums"
	traderepo "app_service/apps/business/trade/repo"
	tradelogic "app_service/apps/business/trade/service/logic"
	"app_service/apps/business/trading_section/dal/model"
	"app_service/apps/business/trading_section/define"
	"app_service/apps/business/trading_section/define/enums"
	"app_service/apps/business/trading_section/repo"
	"app_service/apps/business/trading_section/service/logic"
	"app_service/apps/platform/common/constant"
	"app_service/apps/platform/issue/dal/model/mongdb"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/go-redis/redis/v8"
)

func (s *Service) GetWebTradingSectionList(req *define.GetWebTradingSectionListReq) (*define.GetWebTradingSectionListResp, error) {
	logPrefix := "GetWebTradingSectionList"
	completeCacheKey := fmt.Sprintf("app_service:trading_section:web_list:%d:%d", req.GetPage(), req.GetPageSize())
	if cacheVal, err := global.REDIS.Get(s.ctx, completeCacheKey).Result(); err == nil {
		if cacheVal != "" {
			resp := &define.GetWebTradingSectionListResp{}
			_ = json.Unmarshal([]byte(cacheVal), resp)
			return resp, nil
		}
	}

	db := repo.GetDB()
	tsSchema := repo.GetQuery().TradingSection
	tsQw := search.NewQueryBuilder().
		Select(tsSchema.ID, tsSchema.Name, tsSchema.PriceChangeRate).
		Eq(tsSchema.Status, enums.TradingSectionStatusEnabled.Val()).
		OrderByDesc(tsSchema.Priority).
		OrderByDesc(tsSchema.UpdatedAt).
		OrderByDesc(tsSchema.ID).
		Build()
	tradingSections, total, err := repo.NewTradingSectionRepo(tsSchema.WithContext(s.ctx)).SelectPage(tsQw, req.GetPage(), req.GetPageSize())
	if err != nil {
		return nil, err
	}
	resp := &define.GetWebTradingSectionListResp{
		List:  make([]*define.GetWebTradingSectionListInfo, 0),
		Total: total,
	}
	if len(tradingSections) == 0 {
		return resp, nil
	}

	// 并发获取每个板块的数据
	var wg sync.WaitGroup
	sem := make(chan struct{}, 5) // 控制并发数
	tsdsSchema := repo.GetQuery().TradingSectionDailyStat
	var closePriceTrendingMap sync.Map
	tsCtx, tsCancel := context.WithTimeout(s.ctx, time.Second*10)
	defer tsCancel()
	for _, ts := range tradingSections {
		wg.Add(1)
		sem <- struct{}{}
		go func(ctx context.Context, tsID int64) {
			defer func() {
				<-sem
				wg.Done()
			}()
			trendingCacheKey := logic.GetClosePriceTrendingCacheKey(tsID)
			cacheVal, err := global.REDIS.Get(ctx, trendingCacheKey).Result()
			if err == nil && cacheVal != "" {
				// 使用缓存数据
				var cacheTrending []*define.GetWebClosePriceDailyData
				_ = json.Unmarshal([]byte(cacheVal), &cacheTrending)
				closePriceTrendingMap.Store(tsID, cacheTrending)
			} else {
				// 统计数据
				qw := search.NewQueryBuilder().
					Select(tsdsSchema.TradingDay, tsdsSchema.ClosePriceRate).
					Eq(tsdsSchema.TradingSectionID, tsID).
					OrderByAsc(tsdsSchema.TradingDay).
					Build()
				stats, err := repo.NewTradingSectionDailyStatRepo(tsdsSchema.WithContext(ctx)).SelectList(qw)
				if err != nil {
					log.Ctx(ctx).Errorf("%s GetWebTradingSectionDailyStat err:%v", logPrefix, err)
				} else {
					closePriceTrending := make([]*define.GetWebClosePriceDailyData, 0)
					for _, stat := range stats {
						closePriceTrending = append(closePriceTrending, &define.GetWebClosePriceDailyData{
							Date:            stat.TradingDay.Format("2006-01-02"),
							PriceChangeRate: stat.ClosePriceRate,
						})
					}
					closePriceTrendingMap.Store(tsID, closePriceTrending)
					// 缓存数据
					trendingBts, _ := json.Marshal(closePriceTrending)
					cacheDuration := time.Minute * 10
					if d, err := logic.GetDailyStatCacheDuration(ctx); err == nil {
						cacheDuration = *d
					}
					_, err = global.REDIS.SetEX(ctx, trendingCacheKey, string(trendingBts), cacheDuration).Result()
					if err != nil {
						log.Ctx(ctx).Errorf("%s cache trending data err:%v", logPrefix, err)
					}
				}
			}
		}(tsCtx, ts.ID)
	}
	wg.Wait()

	// 并发获取各个板块涨跌幅最高的 3 个商品
	var priceChangeWg sync.WaitGroup
	priceChangeSem := make(chan struct{}, 5)
	var priceChangeItemsMap sync.Map
	statCtx, statCancel := context.WithTimeout(s.ctx, time.Second*10)
	defer statCancel()
	for _, ts := range tradingSections {
		tsID := ts.ID
		priceChangeWg.Add(1)
		priceChangeSem <- struct{}{}
		go func(ctx context.Context, tsID int64) {
			defer func() {
				<-priceChangeSem
				priceChangeWg.Done()
			}()
			cacheKey := logic.GetTopItemListCacheKey(tsID)
			cacheVal, err := global.REDIS.Get(ctx, cacheKey).Result()
			if err == nil && cacheVal != "" {
				var priceChangeItemList []*define.GetWebTradingSectionListItemInfo
				_ = json.Unmarshal([]byte(cacheVal), &priceChangeItemList)
				priceChangeItemsMap.Store(tsID, priceChangeItemList)
			} else {
				var items []*trademodel.CirculationItem
				err = db.Table(model.TableNameTradingSectionItem).
					Select("ci.*").
					Where("trading_section_item.trading_section_id=?", tsID).
					Joins("INNER JOIN circulation_item ci ON ci.item_id = trading_section_item.item_id AND ci.is_display =  ?", tradeenums.CirculationItemDisplayYes.Val()).
					Order("ci.price_change_rate DESC, ci.id DESC").
					Limit(3).
					Find(&items).Error
				if err != nil {
					log.Ctx(ctx).Errorf("%s get price change list err:%v", logPrefix, err)
				} else {
					priceChangeItemList := make([]*define.GetWebTradingSectionListItemInfo, 0)
					for _, item := range items {
						priceChangeItemList = append(priceChangeItemList, &define.GetWebTradingSectionListItemInfo{
							ItemID:   item.ItemID,
							ItemName: item.ItemName,
							ImageURL: item.ImageURL,
						})
					}
					priceChangeItemsMap.Store(tsID, priceChangeItemList)
					// 缓存数据
					priceChangeItemListBts, _ := json.Marshal(priceChangeItemList)
					_, err = global.REDIS.SetEX(ctx, cacheKey, string(priceChangeItemListBts), time.Minute*5).Result()
					if err != nil {
						log.Ctx(ctx).Errorf("%s cache price change list err:%v", logPrefix, err)
					}
				}
			}
		}(statCtx, tsID)
	}
	priceChangeWg.Wait()

	// 查询每个板块有效的商品数量
	tsIDs := make([]int64, 0)
	for _, ts := range tradingSections {
		tsIDs = append(tsIDs, ts.ID)
	}
	itemCountMap, err := logic.GetWebItemCountMap(s.ctx, tsIDs)
	if err != nil {
		return nil, err
	}

	list := make([]*define.GetWebTradingSectionListInfo, 0)
	for _, ts := range tradingSections {
		topList := make([]*define.GetWebTradingSectionListItemInfo, 0)
		if l, ok := priceChangeItemsMap.Load(ts.ID); ok {
			topList = l.([]*define.GetWebTradingSectionListItemInfo)
		}
		closePriceTrending := make([]*define.GetWebClosePriceDailyData, 0)
		if t, ok := closePriceTrendingMap.Load(ts.ID); ok {
			closePriceTrending = t.([]*define.GetWebClosePriceDailyData)
		}
		info := &define.GetWebTradingSectionListInfo{
			ID:                 ts.ID,
			Name:               ts.Name,
			ItemTotal:          itemCountMap[ts.ID],
			TopItemList:        topList,
			PriceChangeRate:    ts.PriceChangeRate,
			ClosePriceTrending: closePriceTrending,
		}
		list = append(list, info)
	}

	resp.List = list

	// 缓存整体结果
	respBts, _ := json.Marshal(resp)
	if _, err := global.REDIS.SetEX(s.ctx, completeCacheKey, string(respBts), time.Second*5).Result(); err != nil {
		log.Ctx(s.ctx).Errorf("%s cache complete err:%v", logPrefix, err)
	}

	return resp, nil
}

func (s *Service) GetWebTradingSectionDetail(req *define.GetWebTradingSectionDetailReq) (*define.GetWebTradingSectionDetailResp, error) {
	logPrefix := "GetWebTradingSectionDetail"
	tsSchema := repo.GetQuery().TradingSection
	qw := search.NewQueryBuilder().
		Eq(tsSchema.ID, req.ID).
		Eq(tsSchema.Status, enums.TradingSectionStatusEnabled.Val()).
		Build()
	tradingSection, err := repo.NewTradingSectionRepo(tsSchema.WithContext(s.ctx)).SelectOne(qw)
	if err != nil {
		return nil, err
	}

	// 查询统计数据
	var statDataList []*model.TradingSectionDailyStat
	statCacheKey := logic.GetStatDataListCacheKey(req.ID)
	statCacheVal, err := global.REDIS.Get(s.ctx, statCacheKey).Result()
	if err == nil && statCacheVal != "" {
		_ = json.Unmarshal([]byte(statCacheVal), &statDataList)
	} else {
		tsdsSchema := repo.GetQuery().TradingSectionDailyStat
		statQw := search.NewQueryBuilder().
			Select(tsdsSchema.TransactionAmount, tsdsSchema.MarketAmount).
			Eq(tsdsSchema.TradingSectionID, req.ID).
			OrderByAsc(tsdsSchema.TradingDay).
			Build()
		statList, err := repo.NewTradingSectionDailyStatRepo(tsdsSchema.WithContext(s.ctx)).SelectList(statQw)
		if err != nil {
			return nil, err
		}
		statDataList = statList
		statDataBts, _ := json.Marshal(statDataList)
		// 缓存
		cacheDuration := time.Minute * 10
		if d, err := logic.GetDailyStatCacheDuration(s.ctx); err == nil {
			cacheDuration = *d
		}
		_, err = global.REDIS.SetEX(s.ctx, statCacheKey, string(statDataBts), cacheDuration).Result()
		if err != nil {
			log.Ctx(s.ctx).Errorf("%s cache stat daily stat err:%v", logPrefix, err)
		}
	}

	transAmountTrending := make([]int64, 0)
	marketAmountTrending := make([]int64, 0)
	for _, stat := range statDataList {
		transAmountTrending = append(transAmountTrending, stat.TransactionAmount)
		marketAmountTrending = append(marketAmountTrending, stat.MarketAmount)
	}

	// 查询商品总数
	itemCountMap, err := logic.GetWebItemCountMap(s.ctx, []int64{req.ID})
	if err != nil {
		return nil, err
	}
	itemTotal := itemCountMap[req.ID]

	timeFormatLayout := "1.2 15:04"
	updateTime := ""
	if tradingSection.StatDataUpdateTime != nil {
		updateTime = tradingSection.StatDataUpdateTime.Format(timeFormatLayout)
	}
	return &define.GetWebTradingSectionDetailResp{
		ID:                        tradingSection.ID,
		Name:                      tradingSection.Name,
		Description:               tradingSection.Description,
		PriceChangeRate:           tradingSection.PriceChangeRate,
		ItemTotal:                 itemTotal,
		TransactionAmount:         tradingSection.TransactionAmount,
		TransactionAmountRatio:    tradingSection.TransactionAmountRatio,
		TransactionAmountTrending: transAmountTrending,
		MarketAmount:              tradingSection.MarketAmount,
		MarketAmountRatio:         tradingSection.MarketAmountRatio,
		MarketAmountTrending:      marketAmountTrending,
		StatDataUpdateTime:        updateTime,
	}, nil
}

func (s *Service) GetWebTradingSectionActivities(req *define.GetWebTradingSectionActivitiesReq) (*define.GetWebTradingSectionActivitiesResp, error) {
	logPrefix := "GetWebActivities"
	channel, _ := s.ctx.Value(constant.AppChannel).(string)
	cacheKey := fmt.Sprintf("app_service:trading_section:activities:%s:%d", channel, req.ID)
	cacheStr, err := global.REDIS.Get(s.ctx, cacheKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		log.Ctx(s.ctx).Errorf("%s redis Get %s failed: %v", logPrefix, cacheKey, err)
	} else if cacheStr != "" {
		data := &define.GetWebTradingSectionActivitiesResp{}
		err := json.Unmarshal([]byte(cacheStr), data)
		if err != nil {
			log.Ctx(s.ctx).Errorf("%s json.Unmarshal error: %v", logPrefix, err)
		} else {
			return data, nil
		}
	}

	emptyItems := make([]define.GetWebActivityItem, 0)
	infoList := make([]define.GetWebActivityData, 0)
	// 平台方公告
	annDB := annrepo.GetDB().WithContext(s.ctx)
	// 使用公共条件
	annWhere, annArgs := annlogic.GetPublishedAndChannelCondition(s.ctx)
	annDB = annDB.Where(annWhere, annArgs...)
	annDB = annDB.Select("announcement.*").
		Joins("INNER JOIN ann_trading_section ats ON ats.ann_id = announcement.ann_id AND ats.trading_section_id = ? ", req.ID)
	annDB = annDB.Order("publish_time DESC")
	var anns []*annmodel.Announcement
	err = annDB.Limit(3).Find(&anns).Error
	if err != nil {
		return nil, err
	}
	for _, item := range anns {
		infoList = append(infoList, define.GetWebActivityData{
			ID:          item.AnnID,
			Type:        enums.ActivityTypePlatformAnn,
			Title:       item.Title,
			PublishTime: *item.PublishTime,
			Items:       emptyItems,
			IsCanJump:   item.Content != "",
		})
	}
	// 运营方公告
	operAnnDB := operannrepo.GetDB().WithContext(s.ctx)
	// 使用公共条件
	operAnnWhere, operArgs := operannlogic.GetPublishedAndChannelCondition(s.ctx)
	operAnnDB = operAnnDB.Where(operAnnWhere, operArgs...)
	operAnnDB = operAnnDB.Select("operation_announcement.*").
		Joins("INNER JOIN oper_ann_trading_section oats ON oats.oper_ann_id = operation_announcement.operation_announcement_id AND oats.trading_section_id = ? ", req.ID)
	operAnnDB = operAnnDB.Order("publish_time DESC")
	var operAnns []*operannmodel.OperationAnnouncement
	err = operAnnDB.Limit(3).Find(&operAnns).Error
	if err != nil {
		return nil, err
	}
	for _, item := range operAnns {
		infoList = append(infoList, define.GetWebActivityData{
			ID:          item.OperationAnnouncementID,
			Type:        enums.ActivityTypeOperationAnn,
			Title:       item.Title,
			PublishTime: *item.PublishTime,
			Items:       emptyItems,
			IsCanJump:   item.Content != "",
		})
	}
	// 行情异动
	mcDb := mcrepo.GetDB().WithContext(s.ctx)
	mcQuery := mcDb.Table(mcmodel.TableNameMarketChanges).
		Preload("Items").
		Where("market_changes.status = ?", mcrenums.MarketChangesStatusPublished.Val()).
		Order("market_changes.publish_time DESC").
		Limit(3)
	// 添加 channel 过滤条件：使用 INNER JOIN
	mcQuery = mcQuery.Joins("INNER JOIN market_changes_channel ON market_changes.market_changes_id = market_changes_channel.market_changes_id").
		Where("market_changes_channel.channel_id = ? OR market_changes_channel.channel_id = 'all'", channel).
		Joins("INNER JOIN market_changes_trading_section mcts ON mcts.market_changes_id = market_changes.market_changes_id AND mcts.trading_section_id = ? ", req.ID)
	mcs := make([]mcmodel.MarketChanges, 0)
	err = mcQuery.Find(&mcs).Error
	if err != nil {
		return nil, err
	}
	var itemIDs []string
	for _, mc := range mcs {
		for _, item := range mc.Items {
			if !util.In(item.ItemID, itemIDs) {
				itemIDs = append(itemIDs, item.ItemID)
			}
		}
	}
	itemMap := map[string]*mongdb.IssueItem{}
	if len(itemIDs) > 0 {
		m, err := issueFacade.GetIssueItemMapByCache(s.ctx, itemIDs)
		if err != nil {
			return nil, err
		}
		itemMap = m
	}
	for _, item := range mcs {
		// 行情异动关联的商品信息
		relateItems := make([]define.GetWebActivityItem, 0)
		if len(item.Items) > 0 {
			for _, relateItem := range item.Items {
				if itemInfo, ok := itemMap[relateItem.ItemID]; ok {
					relateItems = append(relateItems, define.GetWebActivityItem{
						ItemId:            relateItem.ItemID,
						ItemName:          relateItem.ItemName,
						PriceChanges:      relateItem.PriceChanges,
						CirculationStatus: itemInfo.CirculationStatus,
						Status:            itemInfo.Status,
					})
				}
			}
		}
		infoList = append(infoList, define.GetWebActivityData{
			ID:          item.MarketChangesID,
			Type:        enums.ActivityTypeMarketChanges,
			Title:       item.Title,
			PublishTime: *item.PublishTime,
			Items:       relateItems,
			IsCanJump:   item.Content != "",
		})
	}
	// 重新排序
	sort.Slice(infoList, func(i, j int) bool {
		return infoList[i].PublishTime.After(infoList[j].PublishTime)
	})

	if len(infoList) > 3 {
		infoList = infoList[:3]
	}

	resp := &define.GetWebTradingSectionActivitiesResp{
		List: infoList,
	}
	// 缓存
	bts, err := json.Marshal(resp)
	if err != nil {
		log.Ctx(s.ctx).Errorf("%s json.Marshal error: %v", logPrefix, err)
	} else {
		_, err = global.REDIS.SetNX(s.ctx, cacheKey, string(bts), time.Second*5).Result()
		if err != nil {
			log.Ctx(s.ctx).Errorf("%s set redis cache error: %v", logPrefix, err)
		}
	}

	return resp, err
}

func (s *Service) GetWebTradingSectionItemList(req *define.GetWebTradingSectionItemListReq) (*define.GetWebTradingSectionItemListResp, error) {
	// 获取该板块的商品数据
	tsiSchema := repo.GetQuery().TradingSectionItem
	tsiQw := search.NewQueryBuilder().
		Select(tsiSchema.ItemID).
		Eq(tsiSchema.TradingSectionID, req.ID).
		Build()
	tsItems, err := repo.NewTradingSectionItemRepo(tsiSchema.WithContext(s.ctx)).SelectList(tsiQw)
	if err != nil {
		return nil, err
	}
	if len(tsItems) == 0 {
		return &define.GetWebTradingSectionItemListResp{}, nil
	}
	itemIDs := make([]string, 0)
	for _, tsItem := range tsItems {
		itemIDs = append(itemIDs, tsItem.ItemID)
	}

	ciSchema := traderepo.GetQuery().CirculationItem
	qb := search.NewQueryBuilder().
		In(ciSchema.ItemID, itemIDs).
		Eq(ciSchema.IsDisplay, tradeenums.CirculationItemDisplayYes.Val()).
		OrderByAsc(ciSchema.IsDelisted) // 已退市的排最后
	isDesc := strings.ToLower(req.SortOrder) == "desc"
	if req.OrderBy != "" && req.SortOrder != "" {
		switch req.OrderBy {
		case tradeenums.CirculationItemOrderByTransactionAmount:
			if isDesc {
				qb.OrderByDesc(ciSchema.TotalTransactionAmount)
			} else {
				qb.OrderByAsc(ciSchema.TotalTransactionAmount)
			}
		case tradeenums.CirculationItemOrderByMarketAmount:
			if isDesc {
				qb.OrderByDesc(ciSchema.MarketAmount)
			} else {
				qb.OrderByAsc(ciSchema.MarketAmount)
			}
		case tradeenums.CirculationItemOrderByPriceChangeRate:
			if isDesc {
				qb.OrderByDesc(ciSchema.PriceChangeRate)
			} else {
				qb.OrderByAsc(ciSchema.PriceChangeRate)
			}
		}
	}
	// 根据主键排序，防止分页少数据
	qb.OrderByDesc(ciSchema.ID)

	// 分页处理
	offset := req.GetOffset()
	limit := req.GetPageSize()
	ciDo := ciSchema.WithContext(s.ctx).Offset(offset).Limit(limit)
	circItems, err := traderepo.NewCirculationItemRepo(ciDo).
		SelectList(qb.Build())
	if err != nil {
		return nil, err
	}
	list := make([]define.GetWebTradingSectionItemInfo, len(circItems))
	for i := 0; i < len(circItems); i++ {
		circItem := circItems[i]
		// 最新成交价优先使用缓存的值
		latestPriceCacheKey := tradelogic.GetCirculationItemLatestPriceCacheKey(circItem.ItemID)
		cacheLatestPrice, _ := global.REDIS.Get(s.ctx, latestPriceCacheKey).Int()
		latestPrice := circItem.LatestTransactionPrice
		if cacheLatestPrice > 0 {
			latestPrice = int64(cacheLatestPrice)
		}
		// 涨跌幅优先使用缓存的值
		priceChangeRate := circItem.PriceChangeRate
		cacheRate, err := tradelogic.GetPriceChangeRateFromCache(s.ctx, circItem.ItemID)
		if err == nil {
			priceChangeRate = float32(cacheRate)
		}
		isDelisted := false
		if circItem.IsDelisted == 1 {
			isDelisted = true
		}
		list[i] = define.GetWebTradingSectionItemInfo{
			ID:                     circItem.ID,
			ItemID:                 circItem.ItemID,
			ItemName:               circItem.ItemName,
			ImageURL:               circItem.ImageURL,
			TotalCirculation:       circItem.TotalCirculation,
			MarketAmount:           circItem.MarketAmount,
			TransactionAmount:      circItem.TotalTransactionAmount,
			LatestTransactionPrice: latestPrice,
			PriceChangeRate:        priceChangeRate,
			IsDelisted:             isDelisted,
		}
	}

	resp := &define.GetWebTradingSectionItemListResp{
		List:    list,
		HasMore: len(list) == req.GetPageSize(),
	}
	return resp, nil
}
