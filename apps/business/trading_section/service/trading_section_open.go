package service

import (
	"app_service/apps/business/trading_section/define"
	"app_service/apps/business/trading_section/service/logic"
	"app_service/pkg/util"
	"context"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
)

func (s *Service) UpdateTradingSectionStatData(req *define.UpdateTradingSectionStatDataReq) (*define.UpdateTradingSectionStatDataResp, error) {
	logPrefix := "UpdateTradingSectionStatData"
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	go func(ctx context.Context, logPrefix string) {
		// 异步更新数据
		startTime := util.Now()
		err := logic.UpdateTradingSectionStatData(ctx)
		if err != nil {
			log.Ctx(ctx).Errorf("%s err:%v", logPrefix, err)
			return
		}
		log.Ctx(ctx).Infof("%s success! duration: %v", logPrefix, time.Since(startTime))
	}(spanCtx, logPrefix)
	return &define.UpdateTradingSectionStatDataResp{}, nil
}

func (s *Service) TradingSectionDailyStat(req *define.TradingSectionDailyStatReq) (*define.TradingSectionDailyStatResp, error) {
	logPrefix := "TradingSectionDailyStat"
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	go func(ctx context.Context, logPrefix string) {
		// 异步更新数据
		startTime := util.Now()
		err := logic.TradingSectionDailyStat(ctx)
		if err != nil {
			log.Ctx(ctx).Errorf("%s err:%v", logPrefix, err)
			return
		}
		log.Ctx(ctx).Infof("%s success! duration: %v", logPrefix, time.Since(startTime))
	}(spanCtx, logPrefix)
	return &define.TradingSectionDailyStatResp{}, nil
}
