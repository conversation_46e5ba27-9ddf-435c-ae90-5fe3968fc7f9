package service

import (
	"app_service/apps/business/trading_section/dal/model"
	"app_service/apps/business/trading_section/define"
	"app_service/apps/business/trading_section/define/enums"
	"app_service/apps/business/trading_section/repo"
	"app_service/apps/business/trading_section/service/locker"
	"app_service/apps/business/trading_section/service/logic"
	"app_service/apps/platform/issue/dal/model/mongdb"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"context"
	"errors"
	"time"

	tradelogic "app_service/apps/business/trade/service/logic"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"gorm.io/gorm"
)

func (s *Service) AddTradingSection(req *define.AddTradingSectionReq) (*define.AddTradingSectionResp, error) {
	section := &model.TradingSection{
		ID:          snowflakeutl.GenerateID(),
		Name:        req.Name,
		Description: req.Description,
		Priority:    req.Priority,
	}
	tsSchema := repo.GetQuery().TradingSection
	err := repo.NewTradingSectionRepo(tsSchema.WithContext(s.ctx)).Save(section)
	if err != nil {
		if errors.Is(err, gorm.ErrDuplicatedKey) || util.IsMySQLDuplicateError(err) {
			return nil, define.SH410001Err
		}
		return nil, err
	}

	return &define.AddTradingSectionResp{
		ID:        section.ID,
		CreatedAt: util.Now().Format(time.RFC3339),
	}, nil
}

func (s *Service) EditTradingSection(req *define.EditTradingSectionReq) (*define.EditTradingSectionResp, error) {
	tsSchema := repo.GetQuery().TradingSection
	qw := search.NewQueryBuilder().Eq(tsSchema.ID, req.ID).Build()
	tradingSection, err := repo.NewTradingSectionRepo(tsSchema.WithContext(s.ctx)).SelectOne(qw)
	if err != nil {
		return nil, err
	}

	issueItemMap := make(map[string]*mongdb.IssueItem)
	if len(req.ItemIDs) > 0 {
		m, err := issueFacade.GetIssueItemMap(s.ctx, req.ItemIDs)
		if err != nil {
			return nil, err
		}
		issueItemMap = m
	}

	// 事务执行
	err = repo.ExecGenTx(s.ctx, func(ctx context.Context) error {
		tsiTxSchema := repo.Query(ctx).TradingSectionItem
		tsiQw := search.NewQueryBuilder().Eq(tsiTxSchema.TradingSectionID, req.ID).Build()
		tsItems, err := repo.NewTradingSectionItemRepo(tsiTxSchema.WithContext(ctx)).SelectList(tsiQw)
		if err != nil {
			return err
		}
		if len(req.ItemIDs) > 0 {
			// 更新商品关联关系
			toDelRecords := make([]*model.TradingSectionItem, 0)
			existsItemIDs := make([]string, 0)
			for _, tsItem := range tsItems {
				if !util.In(tsItem.ItemID, req.ItemIDs) {
					toDelRecords = append(toDelRecords, tsItem)
				}
				existsItemIDs = append(existsItemIDs, tsItem.ItemID)
			}
			// 删除多余的关联
			if len(toDelRecords) > 0 {
				err = repo.NewTradingSectionItemRepo(tsiTxSchema.WithContext(ctx)).RemoveByIds(toDelRecords...)
				if err != nil {
					return err
				}
			}
			toAddRecords := make([]*model.TradingSectionItem, 0)
			for _, itemID := range req.ItemIDs {
				if !util.In(itemID, existsItemIDs) {
					issueItem := issueItemMap[itemID]
					toAddRecords = append(toAddRecords, &model.TradingSectionItem{
						TradingSectionID: tradingSection.ID,
						ItemID:           itemID,
						ItemName:         issueItem.ItemName,
						ImageURL:         issueItem.ImageURL,
					})
				}
			}
			// 新增关联
			if len(toAddRecords) > 0 {
				err = repo.NewTradingSectionItemRepo(tsiTxSchema.WithContext(ctx)).BatchSave(toAddRecords, len(toAddRecords))
				if err != nil {
					return err
				}
			}
		} else if len(tsItems) > 0 {
			// 移除商品
			err = repo.NewTradingSectionItemRepo(tsiTxSchema.WithContext(ctx)).RemoveByIds(tsItems...)
			if err != nil {
				return err
			}
		}

		// 更新主表
		tsTxSchema := repo.Query(ctx).TradingSection
		updateParams := map[string]interface{}{
			"name":        req.Name,
			"description": req.Description,
			"priority":    req.Priority,
		}
		updateQw := search.NewQueryBuilder().Eq(tsTxSchema.ID, req.ID).Build()
		err = repo.NewTradingSectionRepo(tsTxSchema.WithContext(ctx)).UpdateField(updateParams, updateQw)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	spanCtx := s.NewContextWithSpanContext(s.ctx)
	if req.Name != tradingSection.Name {
		// 异步更新关联板块名称
		go func(ctx context.Context) {
			// 关联数据
			logic.UpdateRelatedTradingSectionName(ctx, tradingSection.ID, req.Name)
		}(spanCtx)
	}
	// 异步清除板块商品列表缓存
	if tradingSection.Status == enums.TradingSectionStatusEnabled.Val() {
		// 清空模块涨跌幅最高的 3 个商品的缓存
		go func(ctx context.Context) {
			cacheKey := logic.GetTopItemListCacheKey(tradingSection.ID)
			_, err = global.REDIS.Del(ctx, cacheKey).Result()
			if err != nil {
				log.Ctx(ctx).Errorf("clear cache key %s err: %v", cacheKey, err)
			}
		}(spanCtx)
	}

	return &define.EditTradingSectionResp{
		ID:        tradingSection.ID,
		UpdatedAt: util.Now().Format(time.RFC3339),
	}, nil
}

func (s *Service) GetAdminTradingSectionDetail(req *define.GetAdminTradingSectionDetailReq) (*define.GetAdminTradingSectionDetailResp, error) {
	tsSchema := repo.GetQuery().TradingSection
	qw := search.NewQueryBuilder().Eq(tsSchema.ID, req.ID).Build()
	tradingSection, err := repo.NewTradingSectionRepo(tsSchema.WithContext(s.ctx)).SelectOne(qw)
	if err != nil {
		return nil, err
	}
	// 查询商品
	tsiSchema := repo.GetQuery().TradingSectionItem
	itemQw := search.NewQueryBuilder().
		Select(tsiSchema.ItemID).
		Eq(tsiSchema.TradingSectionID, req.ID).
		Build()
	items, err := repo.NewTradingSectionItemRepo(tsiSchema.WithContext(s.ctx)).SelectList(itemQw)
	if err != nil {
		return nil, err
	}
	itemIDs := make([]string, 0)
	for _, item := range items {
		itemIDs = append(itemIDs, item.ItemID)
	}
	itemList := make([]*define.GetAdminTradingSectionDetailItemInfo, 0)
	if len(itemIDs) > 0 {
		issueItemMap, err := issueFacade.GetIssueItemMap(s.ctx, itemIDs)
		if err != nil {
			return nil, err
		}
		// 流通数量
		itemCountMap, err := tradelogic.GetTotalCirculationMapFromYc(s.ctx, itemIDs)
		if err != nil {
			return nil, err
		}
		// 最新成交价
		latestPriceMap, err := issueFacade.GetLatestClosePriceMapByItemIDs(s.ctx, itemIDs)
		if err != nil {
			return nil, err
		}
		// 组装数据
		now := util.Now()
		for _, itemID := range itemIDs {
			issueItem := issueItemMap[itemID]
			ycItemCirculationCount := itemCountMap[itemID]
			latestPrice := latestPriceMap[itemID]
			// 流通数量 = 该商品首发剩余库存(首发已结束不计算)+该商品当前总用户持仓数量(排除已提货、已融合的，持仓数量每小时计算更新一次)
			totalCirculation := ycItemCirculationCount
			if issueItem.SaleEnd == nil || issueItem.SaleEnd.After(now) {
				totalCirculation = totalCirculation + int64(issueItem.Quantity) - int64(issueItem.SalesVolume)
			}
			// 计算市值
			marketAmount := totalCirculation * int64(latestPrice)
			item := &define.GetAdminTradingSectionDetailItemInfo{
				ItemID:          itemID,
				ItemName:        issueItem.ItemName,
				ImageURL:        issueItem.ImageURL,
				IPClassifyNames: issueItem.IPClassifyNames,
				HolderQuantity:  totalCirculation,
				MarketAmount:    marketAmount,
				LastOrderAmount: latestPrice,
			}
			itemList = append(itemList, item)
		}
	}

	return &define.GetAdminTradingSectionDetailResp{
		ID:          tradingSection.ID,
		Name:        tradingSection.Name,
		Description: tradingSection.Description,
		Priority:    tradingSection.Priority,
		ItemList:    itemList,
		Status:      tradingSection.Status,
		CreatedAt:   tradingSection.CreatedAt,
		UpdatedAt:   tradingSection.UpdatedAt,
	}, nil
}

func (s *Service) UpdateTradingSectionStatus(req *define.UpdateTradingSectionStatusReq) (*define.UpdateTradingSectionStatusResp, error) {
	tsSchema := repo.GetQuery().TradingSection
	qw := search.NewQueryBuilder().Eq(tsSchema.ID, req.ID).Build()
	tradingSection, err := repo.NewTradingSectionRepo(tsSchema.WithContext(s.ctx)).SelectOne(qw)
	if err != nil {
		return nil, err
	}

	if *req.Status == tradingSection.Status {
		return nil, define.SH410002Err
	}

	updateParams := map[string]interface{}{
		"status": *req.Status,
	}
	updateQw := search.NewQueryBuilder().Eq(tsSchema.ID, req.ID).Build()
	err = repo.NewTradingSectionRepo(tsSchema.WithContext(s.ctx)).UpdateField(updateParams, updateQw)
	if err != nil {
		return nil, err
	}

	if *req.Status == enums.TradingSectionStatusEnabled.Val() {
		// 启用立即触发数据更新
		spanCtx := s.NewContextWithSpanContext(s.ctx)
		go func(ctx context.Context) {
			_ = logic.UpdateTradingSectionStatData(ctx)
		}(spanCtx)
	}

	return &define.UpdateTradingSectionStatusResp{
		ID:        tradingSection.ID,
		UpdatedAt: util.Now().Format(time.RFC3339),
	}, nil
}

func (s *Service) UpdateTradingSectionPriority(req *define.UpdateTradingSectionPriorityReq) (*define.UpdateTradingSectionPriorityResp, error) {
	tsSchema := repo.GetQuery().TradingSection
	qw := search.NewQueryBuilder().Eq(tsSchema.ID, req.ID).Build()
	tradingSection, err := repo.NewTradingSectionRepo(tsSchema.WithContext(s.ctx)).SelectOne(qw)
	if err != nil {
		return nil, err
	}

	updateParams := map[string]interface{}{
		"priority": req.Priority,
	}
	updateQw := search.NewQueryBuilder().Eq(tsSchema.ID, req.ID).Build()
	err = repo.NewTradingSectionRepo(tsSchema.WithContext(s.ctx)).UpdateField(updateParams, updateQw)
	if err != nil {
		return nil, err
	}

	return &define.UpdateTradingSectionPriorityResp{
		ID:        tradingSection.ID,
		UpdatedAt: util.Now().Format(time.RFC3339),
	}, nil
}

func (s *Service) GetAdminTradingSectionList(req *define.GetAdminTradingSectionListReq) (*define.GetAdminTradingSectionListResp, error) {
	db := repo.GetDB().WithContext(s.ctx)
	if req.ID > 0 {
		db = db.Where("id = ?", req.ID)
	}
	if req.Name != "" {
		db = db.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}
	if req.ItemID != "" {
		db = db.Where("EXISTS (SELECT 1 FROM trading_section_item tsi WHERE tsi.trading_section_id=trading_section.id AND tsi.item_id = ? )", req.ItemID)
	}
	if req.ItemName != "" {
		db = db.Where("EXISTS (SELECT 1 FROM trading_section_item tsi WHERE tsi.trading_section_id=trading_section.id AND MATCH(tsi.item_name) AGAINST(? IN BOOLEAN MODE) )", req.ItemName)
	}
	// 查询总数
	var total int64
	err := db.Model(&model.TradingSection{}).Count(&total).Error
	if err != nil {
		return nil, err
	}
	// 分页数据
	var tradingSections []*model.TradingSection
	err = db.Limit(req.GetPageSize()).
		Offset(req.GetOffset()).
		Order("id DESC").
		Find(&tradingSections).Error
	if err != nil {
		return nil, err
	}

	var itemQtyMap = make(map[int64]int64)
	if len(tradingSections) > 0 {
		tsIDs := make([]int64, len(tradingSections))
		for i, ts := range tradingSections {
			tsIDs[i] = ts.ID
		}
		// 查询每个板块的商品数量
		type countRecord struct {
			TradingSectionID int64 `gorm:"column:trading_section_id"`
			ItemQty          int64 `gorm:"column:item_qty"`
		}
		var countRecords []*countRecord
		tsiDB := repo.GetDB().WithContext(s.ctx)
		err := tsiDB.Model(&model.TradingSectionItem{}).
			Where("trading_section_id IN (?)", tsIDs).
			Select("trading_section_id, COUNT(*) AS item_qty").
			Group("trading_section_id").
			Find(&countRecords).
			Error
		if err != nil {
			return nil, err
		}

		for _, item := range countRecords {
			itemQtyMap[item.TradingSectionID] = item.ItemQty
		}
	}

	list := make([]*define.GetAdminTradingSectionListInfo, len(tradingSections))
	for i, item := range tradingSections {
		list[i] = &define.GetAdminTradingSectionListInfo{
			ID:                item.ID,
			Name:              item.Name,
			Description:       item.Description,
			Priority:          item.Priority,
			PriceChangeRate:   item.PriceChangeRate,
			TransactionAmount: item.TransactionAmount,
			MarketAmount:      item.MarketAmount,
			ItemQty:           itemQtyMap[item.ID],
			Status:            item.Status,
			CreatedAt:         item.CreatedAt,
			UpdatedAt:         item.UpdatedAt,
		}
	}

	return &define.GetAdminTradingSectionListResp{
		List:  list,
		Total: total,
	}, nil
}

func (s *Service) RefreshTradingSectionDailyStat(req *define.RefreshTradingSectionDailyStatReq) (*define.RefreshTradingSectionDailyStatResp, error) {
	tsSchema := repo.GetQuery().TradingSection
	qw := search.NewQueryBuilder().Eq(tsSchema.ID, req.ID).Build()
	tradingSection, err := repo.NewTradingSectionRepo(tsSchema.WithContext(s.ctx)).SelectOne(qw)
	if err != nil {
		return nil, err
	}

	// 异步刷新数据
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewTradingSectionLock(util.StrVal(req.ID), locker.RefreshDailyStat)))
	if !l.Lock(spanCtx) {
		return nil, response.TooManyRequestErr
	}
	go func(ctx context.Context, tradingSection *model.TradingSection, locker *redis_locker.Locker) {
		defer locker.UnLock(ctx)
		startTime := util.Now()
		refreshErr := logic.RefreshDailyStat(ctx, tradingSection)
		if refreshErr != nil {
			log.Ctx(ctx).Errorf("RefreshDailyStat(%d) failed! err: %v", tradingSection.ID, refreshErr)
		} else {
			log.Ctx(ctx).Infof("RefreshDailyStat(%d) success! duration: %v", tradingSection.ID, time.Since(startTime))
			// 清除相关缓存
			statCacheKey := logic.GetStatDataListCacheKey(req.ID)
			_, cacheErr := global.REDIS.Del(ctx, statCacheKey).Result()
			if cacheErr != nil {
				log.Ctx(ctx).Errorf("clear cache %s failed! err: %v", statCacheKey, cacheErr)
			}
			closePriceTrendingCacheKey := logic.GetClosePriceTrendingCacheKey(req.ID)
			_, cacheErr = global.REDIS.Del(ctx, closePriceTrendingCacheKey).Result()
			if cacheErr != nil {
				log.Ctx(ctx).Errorf("clear cache %s failed! err: %v", closePriceTrendingCacheKey, cacheErr)
			}
		}
	}(spanCtx, tradingSection, l)

	return &define.RefreshTradingSectionDailyStatResp{
		ID: tradingSection.ID,
	}, nil
}
