package logic

import "fmt"

func GetTopItemList<PERSON>ache<PERSON>ey(tsID int64) string {
	return fmt.Sprintf("app_service:trading_section:top_list:%d", tsID)
}

func GetClosePriceTrendingCacheKey(tsID int64) string {
	return fmt.Sprintf("app_service:trading_section:close_price_trending:%d", tsID)
}

func GetStatDataListCacheKey(tsID int64) string {
	return fmt.Sprintf("app_service:trading_section:stat_data_list:%d", tsID)
}
