package logic

import (
	tradelogic "app_service/apps/business/trade/service/logic"
	"app_service/apps/business/trading_section/dal/model"
	"app_service/apps/business/trading_section/define/enums"
	"app_service/apps/business/trading_section/repo"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"app_service/third_party/tmt"
	"context"
	"errors"
	"fmt"
	"sort"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/shopspring/decimal"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type itemSnapshot struct {
	ItemID           string  `json:"item_id"`
	TotalCirculation int64   `json:"total_circulation"`  // 流通数量
	LatestClosePrice int32   `json:"latest_close_price"` // 最新收盘价（当天收盘价）
	SecondClosePrice int32   `json:"second_close_price"` // 上一个交易日收盘价
	LatestTradingDay string  `json:"latest_trading_day"` // 最新交易日日期
	SecondTradingDay *string `json:"second_trading_day"` // 上一个交易日日期
	ClosePriceRate   float32 `json:"close_price_rate"`   // 收盘价涨跌幅
}

// TradingSectionDailyStat 每天收盘后定时更新板块分区统计数据
func TradingSectionDailyStat(ctx context.Context) error {
	logPrefix := "TradingSectionDailyStat"
	isHoliday, err := tmt.IsHoliday(ctx)
	if err != nil {
		return err
	}
	// 非交易日不计算
	if isHoliday {
		log.Ctx(ctx).Infof("%s today is holiday, skip.", logPrefix)
		return nil
	}
	// 未闭市不计算
	timeWindow := issueFacade.GetTimeWindow(ctx)
	if timeWindow != issueFacade.TimeWindowClose {
		log.Ctx(ctx).Infof("%s market is not closed, skip.", logPrefix)
		return nil
	}

	// 查询所有开启的板块
	tsSchema := repo.GetQuery().TradingSection
	tsQw := search.NewQueryBuilder().
		Eq(tsSchema.Status, enums.TradingSectionStatusEnabled.Val()).
		Build()
	tradingSections, err := repo.NewTradingSectionRepo(tsSchema.WithContext(ctx)).SelectList(tsQw)
	if err != nil {
		return err
	}

	// 逐个板块更新
	nowTime := util.Now()
	todayStart := util.GetStartOfDay(nowTime)
	preTradingDate, err := issueFacade.GetPreTradingDate(ctx)
	if err != nil {
		return err
	}
	// 上一个交易日同一时间段
	preEndTime := nowTime.Add(-24 * time.Hour)
	preStartTime := util.GetStartOfDay(preEndTime)
	if preTradingDate != "" {
		preDate, err := time.Parse("2006-01-02", preTradingDate)
		if err != nil {
			return err
		}
		preStartTime = util.GetStartOfDay(preDate)
		preEndTime = time.Date(preDate.Year(), preDate.Month(), preDate.Day(), nowTime.Hour(), nowTime.Minute(), nowTime.Second(), nowTime.Nanosecond(), nowTime.Location())
	}
	for _, tradingSection := range tradingSections {
		calcStatParams, err := getCalculateStatDataParams(ctx, tradingSection.ID, todayStart, nowTime, preStartTime, preEndTime)
		if err != nil {
			return err
		}
		if calcStatParams == nil {
			continue
		}
		statData, err := calculateTradingSectionStatData(ctx, calcStatParams)
		if err != nil {
			return err
		}
		// 计算收盘价涨跌幅
		itemIDs := calcStatParams.itemIDs
		issueItemMap := calcStatParams.issueItemMap
		ycCountMap := calcStatParams.ycCountMap
		var totalMarketAmount int64                         // 各商品当天总市值，商品市值计算公式：当天流通数量*当天收盘价
		totalMarketAmountWithClosePriceRate := decimal.Zero // Σ [ (板块内各商品收盘价涨跌幅 % × 各商品当天市值) ]
		d100 := decimal.NewFromInt(100)
		itemSnapshots := make([]*itemSnapshot, 0)
		for _, itemID := range itemIDs {
			if issueItem, ok := issueItemMap[itemID]; ok {
				itemSnapshotData := &itemSnapshot{
					ItemID: itemID,
				}
				closePriceRecords, err := issueFacade.GetLatestClosePriceRecords(ctx, issueItem.ItemID.Hex(), 2)
				if err != nil {
					return err
				}
				var latestClosePrice int32          // 最新收盘价
				secondClosePrice := issueItem.Price // 次新收盘价（用发行价兜底）
				if len(closePriceRecords) > 0 {
					latestClosePrice = closePriceRecords[0].ClosePrice
					itemSnapshotData.LatestTradingDay = closePriceRecords[0].Date
					if len(closePriceRecords) > 1 {
						secondClosePrice = closePriceRecords[1].ClosePrice
						itemSnapshotData.SecondTradingDay = &closePriceRecords[1].Date
					}
				}
				var closePriceRate float32 // 商品收盘价涨跌幅
				if latestClosePrice > 0 {
					secondPrice := decimal.NewFromInt32(secondClosePrice)
					diff := decimal.NewFromInt32(latestClosePrice).Sub(secondPrice)
					r, _ := diff.Mul(d100).Div(secondPrice).Round(2).Float64()
					closePriceRate = float32(r)
				}
				itemSnapshotData.ClosePriceRate = closePriceRate
				totalCirculation := ycCountMap[itemID]
				if issueItem.SaleEnd == nil || issueItem.SaleEnd.After(nowTime) {
					totalCirculation = totalCirculation + int64(issueItem.Quantity) - int64(issueItem.SalesVolume)
				}
				itemSnapshotData.TotalCirculation = totalCirculation
				var itemMarketAmount int64 // 商品当天市值：当天流通数量*当天收盘价
				if latestClosePrice > 0 {
					itemMarketAmount = totalCirculation * int64(latestClosePrice)
				}
				if itemMarketAmount > 0 {
					val := decimal.NewFromFloat32(closePriceRate).Mul(decimal.NewFromInt(itemMarketAmount)).Div(d100)
					totalMarketAmountWithClosePriceRate = totalMarketAmountWithClosePriceRate.Add(val)
					totalMarketAmount += itemMarketAmount
				}

				itemSnapshotData.LatestClosePrice = latestClosePrice
				itemSnapshotData.SecondClosePrice = secondClosePrice
				itemSnapshots = append(itemSnapshots, itemSnapshotData)
			}
		}
		// 收盘价涨跌幅：Σ [ (板块内各商品收盘价涨跌幅 % × 各商品当天市值) ] / 板块内所有商品当天总市值
		closePriceRate, _ := totalMarketAmountWithClosePriceRate.Div(decimal.NewFromInt(totalMarketAmount)).Mul(d100).Round(2).Float64()
		upsertParams := &upsertDailyStatParams{
			tradingSectionID: tradingSection.ID,
			tradingDay:       todayStart,
			statData:         statData,
			closePriceRate:   float32(closePriceRate),
			itemsSnapshot:    itemSnapshots,
		}
		err = upsertTradingSectionDailyStat(ctx, upsertParams)
		if err != nil {
			return err
		}
	}

	return nil
}

type upsertDailyStatParams struct {
	tradingSectionID int64
	tradingDay       time.Time
	statData         *tradingSectionStatData
	closePriceRate   float32
	itemsSnapshot    []*itemSnapshot
}

func upsertTradingSectionDailyStat(ctx context.Context, params *upsertDailyStatParams) error {
	tradingSectionID := params.tradingSectionID
	tradingDay := params.tradingDay
	statData := params.statData
	closePriceRate := params.closePriceRate
	itemsSnapshot := params.itemsSnapshot

	tsdsSchema := repo.GetQuery().TradingSectionDailyStat
	tsdsQw := search.NewQueryBuilder().
		Eq(tsdsSchema.TradingSectionID, tradingSectionID).
		Eq(tsdsSchema.TradingDay, tradingDay).
		Build()
	dailyStat, err := repo.NewTradingSectionDailyStatRepo(tsdsSchema.WithContext(ctx)).SelectOne(tsdsQw)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	itemsSnapshotJson := datatypes.JSON(util.Obj2JsonStr(itemsSnapshot))
	if dailyStat == nil {
		// 新增
		dailyStatToSave := &model.TradingSectionDailyStat{
			ID:                     snowflakeutl.GenerateID(),
			TradingSectionID:       tradingSectionID,
			TradingDay:             tradingDay,
			ClosePriceRate:         closePriceRate,
			MarketAmount:           statData.todayTotalMarketAmount,
			MarketAmountRatio:      statData.marketAmountRatio,
			TransactionAmount:      statData.todayTotalTransactionAmount,
			TransactionAmountRatio: statData.transactionAmountRatio,
			ItemsSnapshot:          &itemsSnapshotJson,
		}
		err := repo.NewTradingSectionDailyStatRepo(tsdsSchema.WithContext(ctx)).Save(dailyStatToSave)
		if err != nil {
			return err
		}
	} else {
		// 更新
		updateParams := map[string]interface{}{
			"close_price_rate":         closePriceRate,
			"market_amount":            statData.todayTotalMarketAmount,
			"market_amount_ratio":      statData.marketAmountRatio,
			"transaction_amount":       statData.todayTotalTransactionAmount,
			"transaction_amount_ratio": statData.transactionAmountRatio,
			"items_snapshot":           &itemsSnapshotJson,
		}
		updateQw := search.NewQueryBuilder().Eq(tsdsSchema.ID, dailyStat.ID).Build()
		err := repo.NewTradingSectionDailyStatRepo(tsdsSchema.WithContext(ctx)).UpdateField(updateParams, updateQw)
		if err != nil {
			return err
		}
	}

	return nil
}

func RefreshDailyStat(ctx context.Context, tradingSection *model.TradingSection) error {
	logPrefix := "RefreshDailyStat"
	// 板块所有商品
	itemIDs, err := GetItemIDsForStat(ctx, tradingSection.ID)
	if err != nil {
		return err
	}
	if len(itemIDs) == 0 {
		log.Ctx(ctx).Errorf("%s 该板块(%d)没有任何可用于计算的商品！", logPrefix, tradingSection.ID)
		return nil
	}
	// 商品的收盘价数据
	closePriceMap, err := issueFacade.GetAllClosePriceMap(ctx, itemIDs)
	if err != nil {
		return err
	}
	statDateItemIDsMap := make(map[string][]string)
	itemDateClosePriceMap := make(map[string]int32)
	for itemID, closePriceList := range closePriceMap {
		for _, record := range closePriceList {
			if _, ok := statDateItemIDsMap[record.Date]; !ok {
				statDateItemIDsMap[record.Date] = []string{itemID}
			} else if !util.In(itemID, statDateItemIDsMap[record.Date]) {
				statDateItemIDsMap[record.Date] = append(statDateItemIDsMap[record.Date], itemID)
			}

			closePriceKey := fmt.Sprintf("%s:%s", itemID, record.Date)
			itemDateClosePriceMap[closePriceKey] = record.ClosePrice
		}
	}
	dateList := make([]string, 0)
	for key, _ := range statDateItemIDsMap {
		dateList = append(dateList, key)
	}
	sort.Slice(dateList, func(i, j int) bool {
		return dateList[i] < dateList[j]
	})

	// 按照日期计算每天的统计数据
	shanghaiLoc, _ := time.LoadLocation("Asia/Shanghai")
	d100 := decimal.NewFromInt32(100)
	for _, statDate := range dateList {
		startTime, err := time.ParseInLocation("2006-01-02", statDate, shanghaiLoc)
		if err != nil {
			return err
		}
		endTime := util.GetEndOfDay(startTime)
		dateItemIDs := statDateItemIDsMap[statDate] // 当天有收盘价的商品
		// 商品当前的流通数量
		ycCountMap, err := tradelogic.GetTotalCirculationMapFromYc(ctx, dateItemIDs)
		if err != nil {
			return err
		}
		// 获取商品数据
		issueItemMap, err := issueFacade.GetIssueItemMap(ctx, dateItemIDs)
		if err != nil {
			return err
		}
		// 当天成交额
		transactionAmountMap, err := issueFacade.GetTransactionAmountMapByTimeAndItem(ctx, startTime, endTime, dateItemIDs)
		if err != nil {
			return err
		}

		totalMarketAmount := decimal.Zero         // 总市值
		preTotalMarketAmount := decimal.Zero      // 上一个交易日总市值
		totalMarketAmountWeighted := decimal.Zero // 各商品市值权重之和
		totalTransactionAmount := decimal.Zero    // 总成交额
		preTotalTransactionAmount := decimal.Zero // 上一个交易日总成交额

		// 遍历当天有收盘价的商品
		itemsSnapshot := make([]*itemSnapshot, 0)
		for _, itemID := range dateItemIDs {
			issueItem := issueItemMap[itemID]
			closePrice := itemDateClosePriceMap[fmt.Sprintf("%s:%s", itemID, statDate)] // 当天的收盘价
			var preClosePrice int32                                                     // 上一个交易日收盘价
			closePriceRecords := closePriceMap[itemID]
			preClosePriceIdx := -1
			for idx, record := range closePriceRecords {
				if record.Date == statDate {
					preClosePriceIdx = idx - 1
				}
			}
			preStatDate := ""
			if preClosePriceIdx >= 0 {
				preClosePrice = closePriceRecords[preClosePriceIdx].ClosePrice
				preStatDate = closePriceRecords[preClosePriceIdx].Date
			} else {
				// 使用首发价兜底
				preClosePrice = issueItem.Price
			}
			// 收盘价涨跌幅
			closePriceDecimal := decimal.NewFromInt32(closePrice)
			preClosePriceDecimal := decimal.NewFromInt32(preClosePrice)
			closePriceDiff := closePriceDecimal.Sub(preClosePriceDecimal)
			closePriceRate := closePriceDiff.Div(preClosePriceDecimal)
			// 流通数量
			totalCirculation := ycCountMap[itemID]
			nowTime := util.Now()
			if issueItem.SaleEnd == nil || issueItem.SaleEnd.After(nowTime) {
				totalCirculation = totalCirculation + int64(issueItem.Quantity) - int64(issueItem.SalesVolume)
			}
			// 该商品的市值：最新流通数量 x 当天收盘价
			itemMarketAmount := decimal.NewFromInt(totalCirculation).Mul(closePriceDecimal)
			itemMarketAmountWeighted := itemMarketAmount.Mul(closePriceRate) // 该商品的市值权重
			totalMarketAmountWeighted = totalMarketAmountWeighted.Add(itemMarketAmountWeighted)
			totalMarketAmount = totalMarketAmount.Add(itemMarketAmount)
			// 上个交易日市值
			itemPreMarketAmount := decimal.NewFromInt(totalCirculation).Mul(preClosePriceDecimal)
			preTotalMarketAmount = preTotalMarketAmount.Add(itemPreMarketAmount)
			// 成交额
			itemTransAmount := decimal.NewFromInt(transactionAmountMap[itemID])
			totalTransactionAmount = totalTransactionAmount.Add(itemTransAmount)
			// 上个交易日成交额
			if preStatDate != "" {
				preStartTime, _ := time.ParseInLocation("2006-01-02", preStatDate, shanghaiLoc)
				preEndTime := util.GetEndOfDay(preStartTime)
				preTransAmountMap, err := issueFacade.GetTransactionAmountMapByTimeAndItem(ctx, preStartTime, preEndTime, []string{itemID})
				if err != nil {
					return err
				}
				preTransAmount := preTransAmountMap[itemID]
				preTotalTransactionAmount = preTotalTransactionAmount.Add(decimal.NewFromInt(preTransAmount))
			}

			cr, _ := closePriceRate.Float64()
			itemsSnapshot = append(itemsSnapshot, &itemSnapshot{
				ItemID:           itemID,
				TotalCirculation: totalCirculation,
				LatestClosePrice: closePrice,
				SecondClosePrice: preClosePrice,
				LatestTradingDay: statDate,
				SecondTradingDay: &preStatDate,
				ClosePriceRate:   float32(cr),
			})
		}

		// 板块收盘价涨跌幅
		closePriceRate, _ := totalMarketAmountWeighted.Div(totalMarketAmount).Mul(d100).Round(2).Float64()
		// 成交额百分比
		transDiff := totalTransactionAmount.Sub(preTotalTransactionAmount)
		// 如果上个交易日没有成交额，分母为 100 （单位：分）
		if preTotalTransactionAmount.IsZero() {
			preTotalTransactionAmount = d100
		}
		transRatio, _ := transDiff.Mul(d100).Div(preTotalTransactionAmount).Round(2).Float64()
		// 市值百分比
		marketAmountDiff := totalMarketAmount.Sub(preTotalMarketAmount)
		marketAmountRatio, _ := marketAmountDiff.Mul(d100).Div(preTotalMarketAmount).Round(2).Float64()
		upsertParams := &upsertDailyStatParams{
			tradingSectionID: tradingSection.ID,
			tradingDay:       startTime,
			statData: &tradingSectionStatData{
				todayTotalTransactionAmount: totalTransactionAmount.IntPart(),
				preTotalTransactionAmount:   preTotalTransactionAmount.IntPart(),
				transactionAmountRatio:      float32(transRatio),
				todayTotalMarketAmount:      totalMarketAmount.IntPart(),
				preTotalMarketAmount:        preTotalMarketAmount.IntPart(),
				marketAmountRatio:           float32(marketAmountRatio),
			},
			closePriceRate: float32(closePriceRate),
			itemsSnapshot:  itemsSnapshot,
		}
		err = upsertTradingSectionDailyStat(ctx, upsertParams)
		if err != nil {
			return err
		}
	}

	return nil
}

func GetDailyStatCacheDuration(ctx context.Context) (*time.Duration, error) {
	marketTimeConfig, err := tmt.GetCustomConfig(ctx, "market_time")
	if err != nil {
		return nil, err
	}

	_, endTime := issueFacade.GetMarketTimeFromConfig(marketTimeConfig)
	nowTime := util.Now()

	var d time.Duration
	// 每天 23:05 定时计算收盘价
	statEndTime := endTime.Add(time.Minute * 10) // 确保收盘价已经计算完成
	if nowTime.Before(statEndTime) {
		// 未计算完当天收盘价，缓存到当天计算完收盘价的时间
		d = statEndTime.Sub(nowTime)
	} else {
		// 已闭市且计算完收盘价，缓存到第二天计算完收盘价
		d = statEndTime.Add(24 * time.Hour).Sub(nowTime)
	}

	return &d, nil
}
