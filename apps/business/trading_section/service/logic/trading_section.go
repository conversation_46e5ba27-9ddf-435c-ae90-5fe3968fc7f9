package logic

import (
	tradeenums "app_service/apps/business/trade/define/enums"
	tradelogic "app_service/apps/business/trade/service/logic"
	"app_service/apps/business/trading_section/dal/model"
	"app_service/apps/business/trading_section/define/enums"
	"app_service/apps/business/trading_section/repo"
	"app_service/apps/platform/issue/dal/model/mongdb"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/third_party/tmt"
	"context"
	"errors"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/shopspring/decimal"
)

func UpdateTradingSectionStatData(ctx context.Context) error {
	logPrefix := "UpdateTradingSectionStatData"
	isHoliday, err := tmt.IsHoliday(ctx)
	if err != nil {
		return err
	}
	// 非交易日不计算
	if isHoliday {
		log.Ctx(ctx).Infof("%s today is holiday, skip.", logPrefix)
		return nil
	}
	timeWindow := issueFacade.GetTimeWindow(ctx)
	// 未开市不计算
	if timeWindow == issueFacade.TimeWindowBeforeTrading {
		log.Ctx(ctx).Infof("%s market is not open, skip.", logPrefix)
		return nil
	}

	// 查询所有开启的板块
	tsSchema := repo.GetQuery().TradingSection
	tsQw := search.NewQueryBuilder().
		Eq(tsSchema.Status, enums.TradingSectionStatusEnabled.Val()).
		Build()
	tradingSections, err := repo.NewTradingSectionRepo(tsSchema.WithContext(ctx)).SelectList(tsQw)
	if err != nil {
		return err
	}

	nowTime := util.Now()
	todayStart := util.GetStartOfDay(nowTime)
	preTradingDate, err := issueFacade.GetPreTradingDate(ctx)
	if err != nil {
		return err
	}
	// 上一个交易日同一时间段
	preEndTime := nowTime.Add(-24 * time.Hour)
	preStartTime := util.GetStartOfDay(preEndTime)
	if preTradingDate != "" {
		preDate, err := time.Parse("2006-01-02", preTradingDate)
		if err != nil {
			return err
		}
		preStartTime = util.GetStartOfDay(preDate)
		preEndTime = time.Date(preDate.Year(), preDate.Month(), preDate.Day(), nowTime.Hour(), nowTime.Minute(), nowTime.Second(), nowTime.Nanosecond(), nowTime.Location())
	}
	// 逐个板块更新
	for _, tradingSection := range tradingSections {
		tsID := tradingSection.ID
		calcStatParams, err := getCalculateStatDataParams(ctx, tsID, todayStart, nowTime, preStartTime, preEndTime)
		if err != nil {
			return err
		}
		if calcStatParams == nil {
			continue
		}
		statData, err := calculateTradingSectionStatData(ctx, calcStatParams)
		if err != nil {
			return err
		}
		// 更新板块数据
		updateParams := map[string]interface{}{
			"price_change_rate":        statData.priceChangeRate,
			"transaction_amount":       statData.todayTotalTransactionAmount,
			"transaction_amount_ratio": statData.transactionAmountRatio,
			"market_amount":            statData.todayTotalMarketAmount,
			"market_amount_ratio":      statData.marketAmountRatio,
			"stat_data_update_time":    nowTime,
		}
		updateQw := search.NewQueryBuilder().Eq(tsSchema.ID, tsID).Build()
		err = repo.NewTradingSectionRepo(tsSchema.WithContext(ctx)).UpdateField(updateParams, updateQw)
		if err != nil {
			return err
		}
	}

	return nil
}

type tradingSectionStatData struct {
	todayTotalTransactionAmount int64   // 当天累计成交额
	preTotalTransactionAmount   int64   // 上个交易日累计成交额
	transactionAmountRatio      float32 // 成交额百分比
	todayTotalMarketAmount      int64   // 当天累计市值
	preTotalMarketAmount        int64   // 上个交易日累计市值
	marketAmountRatio           float32 // 市值百分比
	priceChangeRate             float32 // 板块涨跌幅
}

// 计算单个板块统计数据
type calculateStatDataParams struct {
	itemIDs                   []string
	issueItemMap              map[string]*mongdb.IssueItem
	todayTransactionAmountMap map[string]int64
	preTransactionAmountMap   map[string]int64
	ycCountMap                map[string]int64
	currentLastSellPriceMap   map[string]int32
	preLastSellPriceMap       map[string]int32
	todayLatestSellPriceMap   map[string]int32
	preClosePriceMap          map[string]int32
	nowTime                   time.Time
}

func getCalculateStatDataParams(ctx context.Context, tsID int64, todayStart, nowTime, preStartTime, preEndTime time.Time) (*calculateStatDataParams, error) {
	logPrefix := "getCalculateStatDataParams"
	// 获取板块的商品列表
	itemIDs, err := GetItemIDsForStat(ctx, tsID)
	if err != nil {
		return nil, err
	}
	if len(itemIDs) == 0 {
		log.Ctx(ctx).Errorf("%s 该板块(%d)没有任何可用于计算的商品！", logPrefix, tsID)
		return nil, nil
	}
	issueItemMap, err := issueFacade.GetIssueItemMap(ctx, itemIDs)
	if err != nil {
		return nil, err
	}
	todayTransactionAmountMap, err := issueFacade.GetTransactionAmountMapByTimeAndItem(ctx, todayStart, nowTime, itemIDs)
	if err != nil {
		return nil, err
	}
	preTransactionAmountMap, err := issueFacade.GetTransactionAmountMapByTimeAndItem(ctx, preStartTime, preEndTime, itemIDs)
	if err != nil {
		return nil, err
	}
	ycCountMap, err := tradelogic.GetTotalCirculationMapFromYc(ctx, itemIDs)
	if err != nil {
		return nil, err
	}
	// 当前最新成交价
	currentLastSellPriceMap, err := issueFacade.GetLatestSellPriceMapBeforeTime(ctx, itemIDs, nowTime)
	if err != nil {
		return nil, err
	}
	// 上个交易日最新成交价
	preLastSellPriceMap, err := issueFacade.GetLatestSellPriceMapBeforeTime(ctx, itemIDs, preEndTime)
	if err != nil {
		return nil, err
	}
	todayLatestSellPriceMap, err := issueFacade.GetTodayLatestSellPriceMapByItemIDs(ctx, itemIDs)
	if err != nil {
		return nil, err
	}
	preClosePriceMap, err := issueFacade.GetPreClosePriceMapByItemIDs(ctx, itemIDs)
	if err != nil {
		return nil, err
	}

	return &calculateStatDataParams{
		itemIDs:                   itemIDs,
		issueItemMap:              issueItemMap,
		todayTransactionAmountMap: todayTransactionAmountMap,
		preTransactionAmountMap:   preTransactionAmountMap,
		currentLastSellPriceMap:   currentLastSellPriceMap,
		preLastSellPriceMap:       preLastSellPriceMap,
		todayLatestSellPriceMap:   todayLatestSellPriceMap,
		preClosePriceMap:          preClosePriceMap,
		ycCountMap:                ycCountMap,
		nowTime:                   nowTime,
	}, nil
}
func calculateTradingSectionStatData(ctx context.Context, params *calculateStatDataParams) (*tradingSectionStatData, error) {
	itemIDs := params.itemIDs
	issueItemMap := params.issueItemMap
	todayTransactionAmountMap := params.todayTransactionAmountMap
	preTransactionAmountMap := params.preTransactionAmountMap
	ycCountMap := params.ycCountMap
	currentLastSellPriceMap := params.currentLastSellPriceMap
	preLastSellPriceMap := params.preLastSellPriceMap
	todayLatestSellPriceMap := params.todayLatestSellPriceMap
	preClosePriceMap := params.preClosePriceMap
	nowTime := params.nowTime

	// 成交额
	var todayTotalTransactionAmount int64 // 今天所有商品总成交额
	for _, amount := range todayTransactionAmountMap {
		todayTotalTransactionAmount = todayTotalTransactionAmount + amount
	}
	var preTotalTransactionAmount int64 // 上个交易日所有商品总成交额
	for _, amount := range preTransactionAmountMap {
		preTotalTransactionAmount = preTotalTransactionAmount + amount
	}
	transDiff := decimal.NewFromInt(todayTotalTransactionAmount).Sub(decimal.NewFromInt(preTotalTransactionAmount))
	// 如果上个交易日没有成交额，分母为 100 （单位：分）
	if preTotalTransactionAmount == 0 {
		preTotalTransactionAmount = 100
	}
	d100 := decimal.NewFromInt(100)
	transRatio, _ := transDiff.Mul(d100).Div(decimal.NewFromInt(preTotalTransactionAmount)).Round(2).Float64()

	// 市值 和 涨跌幅
	var todayTotalMarketAmount int64                     // 今天总市值
	var preTotalMarketAmount int64                       // 上个交易日总市值
	totalMarketAmountWithPriceChangeRate := decimal.Zero // Σ [ (板块内各商品涨跌幅 % × 各商品各自当前市值) ]
	for _, itemID := range itemIDs {
		if issueItem, ok := issueItemMap[itemID]; ok {
			totalCirculation := ycCountMap[itemID]
			if issueItem.SaleEnd == nil || issueItem.SaleEnd.After(nowTime) {
				totalCirculation = totalCirculation + int64(issueItem.Quantity) - int64(issueItem.SalesVolume)
			}

			// 今日市值
			todayPrice := currentLastSellPriceMap[itemID]
			if todayPrice == 0 {
				todayPrice = issueItem.Price
			}
			todayMarketAmount := int64(todayPrice) * totalCirculation // 商品当前最新市值
			todayTotalMarketAmount += todayMarketAmount

			// 上个交易日市值
			prePrice := preLastSellPriceMap[itemID]
			if prePrice == 0 {
				prePrice = issueItem.Price
			}
			preTotalMarketAmount += int64(prePrice) * totalCirculation

			// 计算单个商品：涨跌幅 x 商品当前市值
			todayLatestSellPrice := todayLatestSellPriceMap[itemID]
			preClosePrice := preClosePriceMap[itemID]
			originalPriceChangeRate, err := tradelogic.CalculateOriginalItemPriceChangeRate(ctx, issueItem, preClosePrice, todayLatestSellPrice)
			if err != nil {
				return nil, err
			}
			itemMarketAmountWithPriceChangeRate := originalPriceChangeRate.
				Mul(decimal.NewFromInt(todayMarketAmount))
			totalMarketAmountWithPriceChangeRate = totalMarketAmountWithPriceChangeRate.Add(itemMarketAmountWithPriceChangeRate)
		}
	}
	// 市值百分比
	marketAmountDiff := decimal.NewFromInt(todayTotalMarketAmount).Sub(decimal.NewFromInt(preTotalMarketAmount))
	marketAmountRatio, _ := marketAmountDiff.Mul(d100).Div(decimal.NewFromInt(preTotalMarketAmount)).Round(2).Float64()
	// 板块涨跌幅：Σ [ (板块内各商品涨跌幅 % × 各商品各自当前市值) ] / 板块内所有商品当前总市值
	priceChangeRate, _ := totalMarketAmountWithPriceChangeRate.Mul(d100).Div(decimal.NewFromInt(todayTotalMarketAmount)).Round(2).Float64()

	return &tradingSectionStatData{
		todayTotalTransactionAmount: todayTotalTransactionAmount,
		preTotalTransactionAmount:   preTotalTransactionAmount,
		transactionAmountRatio:      float32(transRatio),
		todayTotalMarketAmount:      todayTotalMarketAmount,
		preTotalMarketAmount:        preTotalMarketAmount,
		marketAmountRatio:           float32(marketAmountRatio),
		priceChangeRate:             float32(priceChangeRate),
	}, nil
}

func UpdateRelatedTradingSectionName(ctx context.Context, tsID int64, tradingSectionName string) {
	logPrefix := "UpdateRelatedTradingSectionName"
	updateParams := map[string]interface{}{
		"trading_section_name": tradingSectionName,
	}

	// 平台方公告关联表
	atsSchema := repo.GetQuery().AnnTradingSection
	atsQw := search.NewQueryBuilder().Eq(atsSchema.TradingSectionID, tsID).Build()
	err := repo.NewAnnTradingSectionRepo(atsSchema.WithContext(ctx)).UpdateField(updateParams, atsQw)
	if err != nil && !errors.Is(err, repo.UpdateFail) {
		log.Ctx(ctx).Errorf("%s update ann trading section name err:%v", logPrefix, err)
	}

	// 运营方公告
	oatsSchema := repo.GetQuery().OperAnnTradingSection
	oatsQw := search.NewQueryBuilder().Eq(oatsSchema.TradingSectionID, tsID).Build()
	err = repo.NewOperAnnTradingSectionRepo(oatsSchema.WithContext(ctx)).UpdateField(updateParams, oatsQw)
	if err != nil && !errors.Is(err, repo.UpdateFail) {
		log.Ctx(ctx).Errorf("%s update oper ann trading section name err:%v", logPrefix, err)
	}

	// 行情异动
	mctsSchema := repo.GetQuery().MarketChangesTradingSection
	mctsQw := search.NewQueryBuilder().Eq(mctsSchema.TradingSectionID, tsID).Build()
	err = repo.NewMarketChangesTradingSectionRepo(mctsSchema.WithContext(ctx)).UpdateField(updateParams, mctsQw)
	if err != nil && !errors.Is(err, repo.UpdateFail) {
		log.Ctx(ctx).Errorf("%s update market changes trading section name err:%v", logPrefix, err)
	}
}

// GetWebItemCountMap 获取各个板块用户端展示的商品数量
func GetWebItemCountMap(ctx context.Context, tsIDs []int64) (map[int64]int64, error) {
	type itemCountRecord struct {
		TradingSectionID int64 `gorm:"column:trading_section_id"`
		ItemCount        int64 `gorm:"column:item_count"`
	}
	var itemCountRecords []*itemCountRecord
	db := repo.GetDB()
	err := db.WithContext(ctx).
		Table(model.TableNameTradingSectionItem).
		Select("trading_section_item.trading_section_id, COUNT(*) AS item_count").
		Where("trading_section_item.trading_section_id IN (?)", tsIDs).
		Joins("INNER JOIN circulation_item ci ON ci.item_id = trading_section_item.item_id AND ci.is_display = ?", tradeenums.CirculationItemDisplayYes.Val()).
		Group("trading_section_item.trading_section_id").
		Find(&itemCountRecords).
		Error
	if err != nil {
		return nil, err
	}
	itemCountMap := make(map[int64]int64)
	for _, item := range itemCountRecords {
		itemCountMap[item.TradingSectionID] = item.ItemCount
	}

	return itemCountMap, nil
}

// GetItemIDsForStat 获取某个板块可用于数据统计的商品 id
// 排除禁止流通、已退市、不在流通时间内的商品
func GetItemIDsForStat(ctx context.Context, tsID int64) ([]string, error) {
	type itemRecord struct {
		ItemID string `gorm:"column:item_id"`
	}
	var itemRecords []*itemRecord
	db := repo.GetDB().WithContext(ctx)
	err := db.Table("trading_section_item tsi").
		Select("DISTINCT ci.item_id").
		Where("tsi.trading_section_id = ?", tsID).
		Joins("INNER JOIN circulation_item ci ON ci.item_id = tsi.item_id AND ci.is_display = ? AND ci.is_delisted = ?",
			tradeenums.CirculationItemDisplayYes.Val(), tradeenums.CirculationItemDelistedNo.Val()).
		Find(&itemRecords).
		Error
	if err != nil {
		return nil, err
	}
	itemIDs := make([]string, 0, len(itemRecords))
	for _, item := range itemRecords {
		itemIDs = append(itemIDs, item.ItemID)
	}

	return itemIDs, nil
}
