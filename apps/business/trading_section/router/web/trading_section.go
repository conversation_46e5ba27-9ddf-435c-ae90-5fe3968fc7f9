package web

import (
	"app_service/apps/business/trading_section/api/web"

	"github.com/gin-gonic/gin"
)

// TradingSection 板块分区客户端相关
func TradingSection(router *gin.RouterGroup) {
	group := router.Group("/trading_section")
	{
		// 获取板块列表
		group.GET("/list", web.GetWebTradingSectionList)
		// 获取板块详情
		group.GET("/detail", web.GetWebTradingSectionDetail)
		// 获取板块活动列表
		group.GET("/activities", web.GetWebTradingSectionActivities)
		// 获取板块商品列表
		group.GET("/item_list", web.GetWebTradingSectionItemList)
	}
}
