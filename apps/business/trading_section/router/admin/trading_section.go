package admin

import (
	"app_service/apps/business/trading_section/api/admin"

	"github.com/gin-gonic/gin"
)

// TradingSection 板块分区管理端相关
func TradingSection(router *gin.RouterGroup) {
	group := router.Group("/trading_section")
	{
		// 新增板块
		group.POST("/add", admin.AddTradingSection)
		// 编辑板块
		group.POST("/edit", admin.EditTradingSection)
		// 获取板块详情
		group.GET("/detail", admin.GetAdminTradingSectionDetail)
		// 更新板块状态
		group.POST("/update_status", admin.UpdateTradingSectionStatus)
		// 更新板块优先级
		group.POST("/update_priority", admin.UpdateTradingSectionPriority)
		// 获取板块列表
		group.GET("/list", admin.GetAdminTradingSectionList)
		// 刷新每日数据统计
		group.POST("/refresh_daily_stat", admin.RefreshTradingSectionDailyStat)
	}
}
