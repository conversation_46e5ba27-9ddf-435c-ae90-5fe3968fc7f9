package router

import (
	"app_service/apps/business/trading_section/router/admin"
	"app_service/apps/business/trading_section/router/open"
	"app_service/apps/business/trading_section/router/web"
	"fmt"

	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/middlewares/g/auth"

	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func init() {
	global.Routers = append(global.Routers, LoadRouter)
}
func LoadRouter() {
	var r *gin.Engine
	engine := global.GetEngine()
	switch engine.(type) {
	case *gin.Engine:
		r = engine.(*gin.Engine)
	default:
		panic(fmt.Sprintf("服务启动失败,不支持的engine"))
	}

	// 管理端路由
	adminRoute(r)
	// 客户端路由
	webRoute(r)
	// 开放路由
	openRoute(r)
}

// 管理端路由
func adminRoute(router *gin.Engine) {
	w := router.Group("/admin/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Admin{
		NoAuthUrl: []string{},
	}))
	admin.TradingSection(w)
}

// 客户端路由
func webRoute(router *gin.Engine) {
	w := router.Group("/web/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Web{
		NoAuthUrl: []string{
			"/web/v1/trading_section/list",
			"/web/v1/trading_section/detail",
			"/web/v1/trading_section/activities",
			"/web/v1/trading_section/item_list",
		},
	}))
	web.TradingSection(w)
}

// 开放路由
func openRoute(router *gin.Engine) {
	w := router.Group("/open/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Open{
		Token: global.GlobalConfig.Service.Token,
	}))
	open.TradingSection(w)
}
