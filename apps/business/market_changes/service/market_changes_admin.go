package service

import (
	"app_service/apps/business/market_changes/dal/model"
	"app_service/apps/business/market_changes/define"
	"app_service/apps/business/market_changes/define/enums"
	"app_service/apps/business/market_changes/repo"
	"app_service/apps/business/market_changes/service/logic"
	tsmodel "app_service/apps/business/trading_section/dal/model"
	tsrepo "app_service/apps/business/trading_section/repo"
	commonDefine "app_service/apps/platform/common/define"
	commonEnum "app_service/apps/platform/common/define/enum"
	commonLogic "app_service/apps/platform/common/service/logic"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"context"
	"encoding/json"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

// GetMarketChangesList 查询行情异动列表
func (s *Service) GetMarketChangesList(req *define.GetMarketChangesAdminListReq) (*define.GetMarketChangesAdminListResp, error) {
	mcSchema := repo.GetQuery().MarketChanges
	query := mcSchema.WithContext(s.ctx)

	// 添加查询条件
	if !req.StartTime.IsZero() && !req.EndTime.IsZero() {
		switch req.TimeType {
		case enums.MarketChangesTimeTypePublish:
			query = query.Where(mcSchema.PublishTime.Gte(req.StartTime), mcSchema.PublishTime.Lte(req.EndTime))
		case enums.MarketChangesTimeTypeCreate:
			query = query.Where(mcSchema.CreatedAt.Gte(req.StartTime), mcSchema.CreatedAt.Lte(req.EndTime))
		}
	}

	if req.ID != 0 {
		query = query.Where(mcSchema.MarketChangesID.Eq(req.ID))
	}

	if req.Title != "" {
		query = query.Where(mcSchema.Title.Like("%" + req.Title + "%"))
	}

	if req.CategoryID != 0 {
		query = query.Where(mcSchema.CategoryID.Eq(req.CategoryID))
	}

	if req.Status != 0 {
		query = query.Where(mcSchema.Status.Eq(int32(req.Status)))
	}

	if req.CreatedBy != "" {
		query = query.Where(mcSchema.CreatedBy.Eq(req.CreatedBy))
	}

	// 处理商品关联条件
	itemSchema := repo.GetQuery().MarketChangesItem
	if req.ItemId != "" || req.ItemName != "" {
		query = query.Join(itemSchema, mcSchema.MarketChangesID.EqCol(itemSchema.MarketChangesID))
		if req.ItemId != "" {
			query = query.Where(itemSchema.ItemID.Eq(req.ItemId))
		}
		if req.ItemName != "" {
			query = query.Where(itemSchema.ItemName.Like("%" + req.ItemName + "%"))
		}
	}

	// 处理渠道关联条件
	channelSchema := repo.GetQuery().MarketChangesChannel
	if req.ChannelId != "" {
		query = query.Join(channelSchema, mcSchema.MarketChangesID.EqCol(channelSchema.MarketChangesID))
		query = query.Where(channelSchema.ChannelID.Eq(req.ChannelId))
	}

	// 处理板块关联关系
	mctsSchema := tsrepo.GetQuery().MarketChangesTradingSection
	if req.TradingSectionID > 0 || req.TradingSectionName != "" {
		query = query.Join(mctsSchema, mcSchema.MarketChangesID.EqCol(mctsSchema.MarketChangesID))
		if req.TradingSectionID > 0 {
			query = query.Where(mctsSchema.TradingSectionID.Eq(req.TradingSectionID))
		}
		if req.TradingSectionName != "" {
			query = query.Where(mctsSchema.TradingSectionName.Like("%" + req.TradingSectionName + "%"))
		}
	}

	// 预加载关联数据
	query = query.Preload(mcSchema.CategoryInfo).
		Preload(mcSchema.Items).
		Preload(mcSchema.Channels)

	// 添加去重
	query = query.Distinct()

	// 排序、分页
	query = query.Order(mcSchema.CreatedAt.Desc())

	// 查询总数
	count, err := query.Count()
	if err != nil {
		return nil, err
	}

	// 分页查询数据
	offset := (req.GetPage() - 1) * req.GetPageSize()
	if offset < 0 {
		offset = 0
	}

	list, err := query.Offset(offset).Limit(req.GetPageSize()).Find()
	if err != nil {
		return nil, err
	}

	resp := &define.GetMarketChangesAdminListResp{
		List:  make([]*define.GetMarketChangesAdminListData, 0),
		Total: count,
	}
	if len(list) == 0 {
		return resp, nil
	}

	var mcIDs []int64
	for _, v := range list {
		mcIDs = append(mcIDs, v.MarketChangesID)
	}
	// 获取关联板块信息
	tradingSectionMap, err := logic.GetTradingSectionMap(s.ctx, mcIDs)
	if err != nil {
		return nil, err
	}

	dataList := make([]*define.GetMarketChangesAdminListData, 0)
	for _, v := range list {
		// 获取分类信息
		var categoryInfo *define.GetCategoryAdminLessDetailResp
		if v.CategoryInfo.CategoryID > 0 {
			categoryInfo = &define.GetCategoryAdminLessDetailResp{
				ID:   v.CategoryInfo.CategoryID,
				Name: v.CategoryInfo.Name,
			}
		}

		// 获取关联商品信息 - 直接使用关联表中的基本信息
		var itemIds []string
		var itemIdInfoList []*define.GetItemIdInfoAdminList
		for _, item := range v.Items {
			itemIdInfoList = append(itemIdInfoList, &define.GetItemIdInfoAdminList{
				ItemId:   item.ItemID,
				ItemName: item.ItemName,
				ImageUrl: item.ImageURL,
			})
			itemIds = append(itemIds, item.ItemID)
		}

		// 获取关联渠道ID
		var channelIds []string
		for _, channel := range v.Channels {
			channelIds = append(channelIds, channel.ChannelID)
		}

		dataList = append(dataList, &define.GetMarketChangesAdminListData{
			ID:              v.MarketChangesID,
			Title:           v.Title,
			Content:         v.Content,
			CategoryID:      v.CategoryID,
			CategoryInfo:    categoryInfo,
			Status:          enums.MarketChangesStatus(v.Status),
			PublishTime:     v.PublishTime,
			ItemIds:         itemIds,
			ItemIdInfoList:  itemIdInfoList,
			ChannelIds:      channelIds,
			PublishType:     enums.MarketChangesPublishType(v.PublishType),
			TradingSections: tradingSectionMap[v.MarketChangesID],
			CreatedBy:       v.CreatedBy,
			CreatedAt:       v.CreatedAt,
			UpdatedAt:       v.UpdatedAt,
		})
	}
	resp.List = dataList
	return resp, nil
}

// GetMarketChangesDetail 查询行情异动详情
func (s *Service) GetMarketChangesDetail(req *define.GetMarketChangesAdminDetailReq) (*define.GetMarketChangesAdminDetailResp, error) {
	mcSchema := repo.GetQuery().MarketChanges
	// 使用Preload预加载关联数据
	marketChangesQuery := mcSchema.WithContext(s.ctx).
		Preload(mcSchema.CategoryInfo).
		Preload(mcSchema.Items).
		Preload(mcSchema.Channels).
		Where(mcSchema.MarketChangesID.Eq(req.ID))

	marketChanges, err := marketChangesQuery.First()
	if err != nil {
		return nil, err
	}

	// 直接使用关联表中的商品信息
	var itemIdInfoList []*define.GetItemIdInfoAdminList
	var itemIds []string
	for _, item := range marketChanges.Items {
		itemIdInfoList = append(itemIdInfoList, &define.GetItemIdInfoAdminList{
			ItemId:   item.ItemID,
			ItemName: item.ItemName,
			ImageUrl: item.ImageURL,
		})
		itemIds = append(itemIds, item.ItemID)
	}

	// 整理渠道ID
	var channelIds []string
	for _, channel := range marketChanges.Channels {
		channelIds = append(channelIds, channel.ChannelID)
	}

	// 构建分类信息
	categoryInfo := &define.GetCategoryAdminLessDetailResp{
		ID:   marketChanges.CategoryInfo.CategoryID,
		Name: marketChanges.CategoryInfo.Name,
	}

	// 获取关联板块信息
	tradingSectionMap, err := logic.GetTradingSectionMap(s.ctx, []int64{marketChanges.MarketChangesID})
	if err != nil {
		return nil, err
	}

	resp := &define.GetMarketChangesAdminDetailResp{
		ID:              marketChanges.MarketChangesID,
		Title:           marketChanges.Title,
		Content:         marketChanges.Content,
		CategoryID:      marketChanges.CategoryID,
		CategoryInfo:    categoryInfo,
		Status:          enums.MarketChangesStatus(marketChanges.Status),
		PublishTime:     marketChanges.PublishTime,
		ItemIds:         itemIds,
		ItemIdInfoList:  itemIdInfoList,
		ChannelIds:      channelIds,
		PublishType:     enums.MarketChangesPublishType(marketChanges.PublishType),
		CreatedBy:       marketChanges.CreatedBy,
		MessagePush:     enums.MarketChangesMessagePushType(marketChanges.MessagePush),
		TradingSections: tradingSectionMap[marketChanges.MarketChangesID],
		CreatedAt:       marketChanges.CreatedAt,
		UpdatedAt:       marketChanges.UpdatedAt,
	}

	return resp, nil
}

// AddMarketChanges 新增行情异动
func (s *Service) AddMarketChanges(req *define.AddMarketChangesReq) (*define.AddMarketChangesResp, error) {

	// 创建行情异动基本信息
	marketChangesID := snowflakeutl.GenerateID()
	marketChanges := &model.MarketChanges{
		MarketChangesID: marketChangesID,
		Title:           req.Title,
		Content:         req.Content,
		CategoryID:      req.CategoryID,
		Status:          enums.MarketChangesStatusDraft.Val(),
		PublishTime:     req.PublishTime,
		PublishType:     req.PublishType.Val(),
		MessagePush:     req.MessagePush.Val(),
		CreatedBy:       s.userService.GetAdminId(),
		UpdatedBy:       s.userService.GetAdminId(),
	}

	// 创建关联商品记录并获取商品信息（如果有关联商品）
	var (
		marketChangesItems []*model.MarketChangesItem
		err                error
	)
	if len(req.ItemIds) > 0 {
		if len(req.ItemIds) > 6 {
			return nil, define.MCH210003Err
		}
		marketChangesItems, err = logic.GetMarketChangesItems(s.ctx, req.ItemIds, marketChangesID)
		if err != nil {
			return nil, err
		}
	}

	// 创建关联渠道记录
	var marketChangesChannels []*model.MarketChangesChannel
	for _, channelId := range req.ChannelIds {
		marketChangesChannels = append(marketChangesChannels, &model.MarketChangesChannel{
			MarketChangesID: marketChangesID,
			ChannelID:       channelId,
		})
	}

	// 关联板块信息
	mcTradingSectionModels := make([]*tsmodel.MarketChangesTradingSection, 0)
	if len(req.TradingSectionIDs) > 0 {
		ms, err := logic.GetMarketChangesTradingSectionModels(s.ctx, req.TradingSectionIDs, marketChangesID)
		if err != nil {
			return nil, err
		}
		mcTradingSectionModels = ms
	}

	// 保存行情异动及关联信息
	// 初始化事务
	err = repo.GetDB().WithContext(s.ctx).Transaction(func(tx *gorm.DB) error {
		// 保存行情异动基本信息
		if err := tx.Create(marketChanges).Error; err != nil {
			return err
		}

		// 保存关联商品信息
		if len(marketChangesItems) > 0 {
			if err := tx.CreateInBatches(marketChangesItems, len(marketChangesItems)).Error; err != nil {
				return err
			}
		}

		// 保存关联渠道信息
		if len(marketChangesChannels) > 0 {
			if err := tx.CreateInBatches(marketChangesChannels, len(marketChangesChannels)).Error; err != nil {
				return err
			}
		}

		// 保存关联板块信息
		if len(mcTradingSectionModels) > 0 {
			if err := tx.Create(mcTradingSectionModels).Error; err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		log.Ctx(s.ctx).Errorf("AddMarketChanges err:%v", err)
		return nil, err
	}

	// 保存操作日志
	go func() {
		ctx := context.Background()
		operatedBy := s.userService.GetAdminId()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:     marketChangesID,
			RelateType:   commonEnum.OperationLogRelateTypeMC,
			Action:       commonEnum.OperationLogActionCreate,
			AfterContent: marketChanges.Content,
			OperatedBy:   operatedBy,
			OperatedAt:   util.Now(),
		})
	}()

	return &define.AddMarketChangesResp{
		ID: marketChangesID,
	}, nil
}

// EditMarketChanges 编辑行情异动
func (s *Service) EditMarketChanges(req *define.EditMarketChangesReq) (*define.EditMarketChangesResp, error) {
	mcSchema := repo.GetQuery().MarketChanges

	// 查询现有数据, 预加载关联项
	query := mcSchema.WithContext(s.ctx).
		Preload(mcSchema.Items).
		Preload(mcSchema.Channels).
		Where(mcSchema.MarketChangesID.Eq(req.ID))

	getMarketChanges, err := query.First()
	if err != nil {
		return nil, err
	}

	// 已下架的行情异动不允许编辑
	if getMarketChanges.Status == enums.MarketChangesStatusOffline.Val() {
		return nil, define.MCH210001Err
	}

	// 决定状态
	var status int32 = getMarketChanges.Status
	if enums.MarketChangesStatus(getMarketChanges.Status) == enums.MarketChangesStatusDraft ||
		enums.MarketChangesStatus(getMarketChanges.Status) == enums.MarketChangesStatusScheduled {
		if req.PublishType == enums.MarketChangesPublishTypeImmediate {
			status = enums.MarketChangesStatusDraft.Val()
		} else if req.PublishType == enums.MarketChangesPublishTypeTiming {
			status = enums.MarketChangesStatusScheduled.Val()
		}
	}

	// 更新行情异动基本信息
	marketChanges := &model.MarketChanges{
		MarketChangesID: req.ID,
		Title:           req.Title,
		Content:         *req.Content,
		CategoryID:      req.CategoryID,
		Status:          status,
		PublishTime:     req.PublishTime,
		PublishType:     req.PublishType.Val(),
		MessagePush:     req.MessagePush.Val(),
		UpdatedBy:       s.userService.GetAdminId(),
	}

	// 创建关联商品记录
	var (
		marketChangesItems []*model.MarketChangesItem
	)
	if len(req.ItemIds) > 0 {
		if len(req.ItemIds) > 6 {
			return nil, define.MCH210003Err
		}
		marketChangesItems, err = logic.GetMarketChangesItems(s.ctx, req.ItemIds, req.ID)
		if err != nil {
			return nil, err
		}

		var itemIds []string
		itemPriceMap := make(map[string]float64)
		for _, item := range getMarketChanges.Items {
			itemIds = append(itemIds, item.ItemID)
			itemPriceMap[item.ItemID] = item.PriceChanges
		}

		// 更新已存在的商品的价格变动
		for _, item := range marketChangesItems {
			if price, ok := itemPriceMap[item.ItemID]; ok {
				item.PriceChanges = price
			}
		}

		// 获取新增的商品ID
		newItemIds := lo.Filter(req.ItemIds, func(id string, _ int) bool {
			return !lo.Contains(itemIds, id)
		})

		// 更新涨跌幅
		if len(newItemIds) > 0 {
			if err := logic.UpdatePriceChanges(s.ctx, marketChangesItems, newItemIds...); err != nil {
				log.Ctx(s.ctx).Errorf("UpdatePriceChanges err:%v", err)
				return nil, err
			}
		}

	}

	// 创建新的关联渠道记录
	var marketChangesChannels []*model.MarketChangesChannel
	for _, channelId := range req.ChannelIds {
		marketChangesChannels = append(marketChangesChannels, &model.MarketChangesChannel{
			MarketChangesID: req.ID,
			ChannelID:       channelId,
		})
	}

	// 关联板块信息
	mcTradingSectionModels := make([]*tsmodel.MarketChangesTradingSection, 0)
	if len(req.TradingSectionIDs) > 0 {
		ms, err := logic.GetMarketChangesTradingSectionModels(s.ctx, req.TradingSectionIDs, req.ID)
		if err != nil {
			return nil, err
		}
		mcTradingSectionModels = ms
	}

	// 使用事务更新行情异动及关联信息
	err = repo.GetDB().WithContext(s.ctx).Transaction(func(tx *gorm.DB) error {
		// 更新行情异动基本信息
		if err := tx.Model(&model.MarketChanges{}).Where("market_changes_id = ?", req.ID).Select(
			"Title", "Content", "CategoryID", "Status", "PublishTime", "PublishType", "MessagePush", "UpdatedBy").Updates(marketChanges).Error; err != nil {
			return err
		}

		// 清除所有关联商品
		if err := tx.Where("market_changes_id = ?", req.ID).Delete(&model.MarketChangesItem{}).Error; err != nil {
			return err
		}

		// 添加新的关联商品
		if len(marketChangesItems) > 0 {
			if err := tx.CreateInBatches(marketChangesItems, len(marketChangesItems)).Error; err != nil {
				return err
			}
		}

		// 清除所有关联渠道
		if err := tx.Where("market_changes_id = ?", req.ID).Delete(&model.MarketChangesChannel{}).Error; err != nil {
			return err
		}

		// 添加新的关联渠道
		if len(marketChangesChannels) > 0 {
			if err := tx.CreateInBatches(marketChangesChannels, len(marketChangesChannels)).Error; err != nil {
				return err
			}
		}

		// 清除所有关联板块
		if err := tx.Where("market_changes_id = ?", req.ID).Delete(&tsmodel.MarketChangesTradingSection{}).Error; err != nil {
			return err
		}

		// 添加新的关联板块信息
		if len(mcTradingSectionModels) > 0 {
			if err := tx.CreateInBatches(mcTradingSectionModels, len(mcTradingSectionModels)).Error; err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		log.Ctx(s.ctx).Errorf("EditMarketChanges err:%v", err)
		return nil, err
	}

	// 处理行情异动缓存
	if status == int32(enums.MarketChangesStatusPublished) {
		// 获取原始渠道信息
		oldChannels := getMarketChanges.Channels
		oldChannelIDs := logic.ExtractChannelIDs(oldChannels)
		newChannelIDs := req.ChannelIds
		// 判断是否发生变更
		added, removed := lo.Difference(newChannelIDs, oldChannelIDs)
		isChannelChanged := len(added) > 0 || len(removed) > 0
		isCategoryChanged := getMarketChanges.CategoryID != req.CategoryID
		if isCategoryChanged || isChannelChanged {
			// 处理所有旧渠道
			oldExpandedChannels := logic.ExpandSpecialChannel(oldChannelIDs)
			if err := logic.RemoveMarketChangesCache(s.ctx, oldExpandedChannels, getMarketChanges); err != nil {
				log.Ctx(s.ctx).Errorf("Failed to remove old cache: %v", err)
			}

			// 处理所有新渠道
			newExpandedChannels := logic.ExpandSpecialChannel(newChannelIDs)
			if err := logic.AddMarketChangesCache(s.ctx, newExpandedChannels, marketChanges); err != nil {
				log.Ctx(s.ctx).Errorf("Failed to add new cache: %v", err)
			}
		}
	}

	// 保存操作日志
	go func() {
		ctx := context.Background()
		operatedBy := s.userService.GetAdminId()
		afterContent, _ := json.Marshal(marketChanges)
		beforeContent, _ := json.Marshal(getMarketChanges)
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:      req.ID,
			RelateType:    commonEnum.OperationLogRelateTypeMC,
			Action:        commonEnum.OperationLogActionUpdate,
			BeforeContent: string(beforeContent),
			AfterContent:  string(afterContent),
			OperatedBy:    operatedBy,
			OperatedAt:    util.Now(),
		})
	}()

	return &define.EditMarketChangesResp{
		ID: req.ID,
	}, nil
}

// EditMarketChangesStatus 编辑行情异动状态
func (s *Service) EditMarketChangesStatus(req *define.EditMarketChangesStatusReq) (*define.EditMarketChangesStatusResp, error) {
	mcSchema := repo.GetQuery().MarketChanges

	// 查询现有数据, 预加载关联项
	query := mcSchema.WithContext(s.ctx).
		Preload(mcSchema.Items).
		Preload(mcSchema.Channels).
		Where(mcSchema.MarketChangesID.Eq(req.ID))

	getMarketChanges, err := query.First()
	if err != nil {
		return nil, err
	}

	m := &model.MarketChanges{
		Status:    req.Status.Val(),
		UpdatedBy: s.userService.GetAdminId(),
	}
	marketChangesItems := make([]*model.MarketChangesItem, 0)

	isPublishOperation := false // 是否为立即发布操作
	if req.Status == enums.MarketChangesStatusOffline {
		// 仅限定时中和已发布的状态的可操作下架
		if !(getMarketChanges.Status == enums.MarketChangesStatusScheduled.Val() ||
			getMarketChanges.Status == enums.MarketChangesStatusPublished.Val()) {
			return nil, define.MCH210002Err
		}
	} else if req.Status == enums.MarketChangesStatusPublished {
		// 仅限待发布的状态可操作发布
		if getMarketChanges.Status != enums.MarketChangesStatusDraft.Val() {
			return nil, define.MCH210007Err
		}

		if getMarketChanges.PublishType == enums.MarketChangesPublishTypeImmediate.Val() {
			isPublishOperation = true
			m.Status = int32(enums.MarketChangesStatusPublished)
			// 更新发布时间
			now := util.Now()
			m.PublishTime = &now
		} else if getMarketChanges.PublishType == enums.MarketChangesPublishTypeTiming.Val() {
			m.Status = int32(enums.MarketChangesStatusScheduled)
		}

		for _, item := range getMarketChanges.Items {
			marketChangesItems = append(marketChangesItems, &model.MarketChangesItem{
				MarketChangesID: item.MarketChangesID,
				ItemID:          item.ItemID,
				ItemName:        item.ItemName,
				ImageURL:        item.ImageURL,
			})
		}
		// 更新涨跌幅
		if err := logic.UpdatePriceChanges(s.ctx, marketChangesItems); err != nil {
			log.Ctx(s.ctx).Errorf("UpdatePriceChanges err:%v", err)
			return nil, err
		}
	} else {
		return nil, define.MCH210005Err
	}

	// 使用事务更新行情异动及关联信息
	err = repo.GetDB().WithContext(s.ctx).Transaction(func(tx *gorm.DB) error {
		// 更新行情异动基本信息
		if err := tx.Model(&model.MarketChanges{}).Where("market_changes_id = ?", req.ID).Updates(m).Error; err != nil {
			return err
		}

		// 更新关联商品
		for _, item := range marketChangesItems {
			if err := tx.Model(&model.MarketChangesItem{}).
				Where("market_changes_id = ? AND item_id = ?", req.ID, item.ItemID).
				Updates(map[string]interface{}{"price_changes": item.PriceChanges}).Error; err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		log.Ctx(s.ctx).Errorf("EditMarketChangesStatus update for marketChangesID %d err:%v", m.MarketChangesID, err)
		return nil, err
	}

	channelIds := logic.ExtractChannelIDs(getMarketChanges.Channels)
	channelIds = logic.ExpandSpecialChannel(channelIds)
	cm := &model.MarketChanges{
		MarketChangesID: getMarketChanges.MarketChangesID,
		CategoryID:      getMarketChanges.CategoryID,
		PublishTime:     m.PublishTime,
	}
	if m.Status == int32(enums.MarketChangesStatusPublished) {
		err := logic.AddMarketChangesCache(s.ctx, channelIds, cm)
		if err != nil {
			log.Ctx(s.ctx).Errorf("AddMarketChangesCache failed: %+v", err)
		}
	}

	if m.Status == int32(enums.MarketChangesStatusOffline) {
		err := logic.RemoveMarketChangesCache(s.ctx, channelIds, cm)
		if err != nil {
			log.Ctx(s.ctx).Errorf("RemoveMarketChangesCache failed: %+v", err)
		}

	}

	// 保存操作日志
	go func() {
		operatedBy := s.userService.GetAdminId()
		beforeContent, _ := json.Marshal(map[string]any{"status": getMarketChanges.Status})
		afterContent, _ := json.Marshal(map[string]any{"status": req.Status})
		var action commonEnum.OperationLogActionEnum
		if req.Status == enums.MarketChangesStatusOffline {
			action = commonEnum.OperationLogActionRemove
		} else if req.Status == enums.MarketChangesStatusPublished {
			action = commonEnum.OperationLogActionPublish
		}
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:      req.ID,
			RelateType:    commonEnum.OperationLogRelateTypeMC,
			Action:        action,
			BeforeContent: string(beforeContent),
			AfterContent:  string(afterContent),
			OperatedBy:    operatedBy,
			OperatedAt:    util.Now(),
		})
	}()

	// 消息推送
	if isPublishOperation && getMarketChanges.MessagePush == enums.MarketChangesMessagePushYes.Val() {
		spanCtx := s.NewContextWithSpanContext(s.ctx)
		go func() {
			pushErr := logic.PushMarketChangesMessage(spanCtx, getMarketChanges)
			if pushErr != nil {
				log.Ctx(spanCtx).Errorf("行情异动消息推送失败: %v", pushErr)
			}
		}()
	}

	return &define.EditMarketChangesStatusResp{
		ID: req.ID,
	}, nil
}
