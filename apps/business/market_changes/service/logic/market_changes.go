package logic

import (
	"app_service/apps/business/market_changes/dal/model"
	"app_service/apps/business/market_changes/define"
	tsmodel "app_service/apps/business/trading_section/dal/model"
	tsrepo "app_service/apps/business/trading_section/repo"
	"app_service/apps/platform/common/constant"
	commonDefine "app_service/apps/platform/common/define"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	"fmt"

	"github.com/go-redis/redis/v8"

	"github.com/shopspring/decimal"

	notidefine "app_service/apps/business/notification/define"
	notienums "app_service/apps/business/notification/define/enums"
	notifacade "app_service/apps/business/notification/facade"

	log "e.coding.net/g-dtay0385/common/go-logger"
)

func GetMarketChangesItems(ctx context.Context, ItemIds []string, marketChangesID int64) ([]*model.MarketChangesItem, error) {
	// 获取商品详情
	issueItems, err := issueFacade.GetIssueItems(ctx, ItemIds)
	if err != nil {
		log.Ctx(ctx).Errorf("GetIssueItems err:%v", err)
		return nil, commonDefine.CommonErr
	}

	// 构建商品ID到详情的映射
	issueItemMap := make(map[string]*struct {
		Name     string
		ImageURL string
	})

	for _, item := range issueItems {
		issueItemMap[item.ItemID.Hex()] = &struct {
			Name     string
			ImageURL string
		}{
			Name:     item.ItemName,
			ImageURL: item.ImageURL,
		}
	}

	// 创建关联商品记录，保存ItemId、ItemName和ImageURL
	var marketChangesItems []*model.MarketChangesItem

	for _, itemId := range ItemIds {
		itemInfo := &struct {
			Name     string
			ImageURL string
		}{
			Name:     "未知商品", // 默认名称
			ImageURL: "",     // 默认空图片
		}

		if info, exists := issueItemMap[itemId]; exists {
			itemInfo = info
		}

		marketChangesItems = append(marketChangesItems, &model.MarketChangesItem{
			MarketChangesID: marketChangesID,
			ItemID:          itemId,
			ItemName:        itemInfo.Name,
			ImageURL:        itemInfo.ImageURL,
		})
	}
	return marketChangesItems, nil
}

// UpdatePriceChanges 更新涨跌幅
func UpdatePriceChanges(ctx context.Context, marketChangesItems []*model.MarketChangesItem, newItemIds ...string) error {
	// 获取需要更新的商品ID
	var itemIDs []string
	if len(newItemIds) > 0 {
		// 使用传入的newItemIds
		itemIDs = newItemIds
	} else {
		// 如果没有指定newItemIds，则更新所有商品
		for _, item := range marketChangesItems {
			itemIDs = append(itemIDs, item.ItemID)
		}
	}

	if len(itemIDs) == 0 {
		return nil // 没有关联商品，不需要计算涨跌幅
	}

	// 获取商品的最新成交价和上一个交易日收盘价
	lastMarketPriceMap, err := issueFacade.GetLatestSellPriceMapByItemIDs(ctx, itemIDs)
	if err != nil {
		return err
	}

	lastClosePriceMap, err := issueFacade.GetPreClosePriceMapByItemIDs(ctx, itemIDs)
	if err != nil {
		return err
	}

	// 计算并更新每个商品的涨跌幅
	for _, item := range marketChangesItems {
		lastMarketPrice, ok := lastMarketPriceMap[item.ItemID]
		if !ok || lastMarketPrice == 0 {
			continue
		}

		lastClosePrice, ok := lastClosePriceMap[item.ItemID]
		if !ok || lastClosePrice == 0 {
			continue
		}

		// 计算涨跌幅
		priceChange := decimal.NewFromInt32(lastMarketPrice).
			Sub(decimal.NewFromInt32(lastClosePrice)).
			Div(decimal.NewFromInt32(lastClosePrice)).
			Mul(decimal.NewFromInt32(100))

		// 保留小数
		roundedPercent := priceChange.Round(2)

		// 特殊处理：如果保留小数后的值为0，但原始值不为0
		if roundedPercent.IsZero() && !priceChange.IsZero() {
			if priceChange.GreaterThan(decimal.Zero) {
				roundedPercent = decimal.NewFromFloat(0.01)
			} else {
				roundedPercent = decimal.NewFromFloat(-0.01)
			}
		}

		// 转换为float64并更新
		item.PriceChanges, _ = roundedPercent.Float64()
	}

	return nil
}

// ExtractChannelIDs 提取渠道ID
func ExtractChannelIDs(channels []model.MarketChangesChannel) []string {
	channelIDs := make([]string, 0, len(channels))
	for _, ch := range channels {
		channelIDs = append(channelIDs, ch.ChannelID)
	}
	return channelIDs
}

// ExpandSpecialChannel 拓展特殊渠道
func ExpandSpecialChannel(channelIDs []string) []string {
	//todo 获取启动的渠道
	allChannels := []string{"1000", "2001"}
	for _, id := range channelIDs {
		if id == "all" {
			return allChannels
		}
	}
	return channelIDs
}

// AddMarketChangesCache 缓存异动行情
func AddMarketChangesCache(ctx context.Context, channelIds []string, m *model.MarketChanges) error {
	pipe := global.REDIS.Pipeline()
	publishTimestamp := float64(m.PublishTime.UnixNano())
	zMember := &redis.Z{
		Score:  publishTimestamp,
		Member: m.MarketChangesID,
	}
	for _, channelId := range channelIds {
		key := constant.GetMarketChangesChannelCategoryListKeyKey(
			channelId,
			m.CategoryID,
		)
		pipe.ZAdd(ctx, key, zMember)

		channelKey := constant.GetMarketChangesChannelListKey(
			channelId,
		)
		pipe.ZAdd(ctx, channelKey, zMember)
	}
	// 提交 Pipeline 并处理错误
	_, err := pipe.Exec(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf("Redis pipeline exec failed: %v", err)
	}
	return err
}

// RemoveMarketChangesCache 移除异动行情缓存
func RemoveMarketChangesCache(ctx context.Context, channelIds []string, m *model.MarketChanges) error {
	pipe := global.REDIS.Pipeline()
	for _, channelId := range channelIds {
		key := constant.GetMarketChangesChannelCategoryListKeyKey(
			channelId,
			m.CategoryID,
		)
		pipe.ZRem(ctx, key, m.MarketChangesID)
		channelKey := constant.GetMarketChangesChannelListKey(
			channelId,
		)
		pipe.ZRem(ctx, channelKey, m.MarketChangesID)
	}
	// 提交 Pipeline 并处理错误
	_, err := pipe.Exec(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf("Redis pipeline exec failed: %v", err)
	}
	return err
}

// PushMarketChangesMessage 推送行情异动消息
func PushMarketChangesMessage(ctx context.Context, marketChanges *model.MarketChanges) error {
	audienceType := notienums.PushAudienceTypeChannel
	for _, ch := range marketChanges.Channels {
		if ch.ChannelID == "all" {
			audienceType = notienums.PushAudienceTypeAll
			break
		}
	}
	extras := make(map[string]interface{})
	pageURL := "ojb://message_list?type=2" // 行情异动列表页
	if marketChanges.Content != "" {
		// 有内容就跳转到详情页
		baseURL := "https://sit-wcjs-acp.ahbq.com.cn"
		if global.GlobalConfig.Service.Env == global.EnvProd {
			baseURL = "https://wcjs-acp.ahbq.com.cn"
		}
		pageURL = fmt.Sprintf("ojb://webview_h5?url=%s/messageDetail?id=%d&type=market_changes",
			baseURL, marketChanges.MarketChangesID)
	}
	extras["url"] = pageURL
	message := notidefine.PushMessage{
		Title:         "行情异动",
		Content:       marketChanges.Title,
		AudienceType:  audienceType,
		ChannelIDs:    ExtractChannelIDs(marketChanges.Channels),
		AndroidExtras: extras,
		IOSExtras:     extras,
	}
	relateInfo := notidefine.PushRelateInfo{
		RelateType:  notienums.PushRelateTypeMarketChanges,
		RelateScene: notienums.PushRelateScenePublish,
		RelateID:    util.StrVal(marketChanges.MarketChangesID),
	}
	result, err := notifacade.PushMessage(ctx, message, relateInfo)
	if err != nil {
		return err
	}

	log.Ctx(ctx).Infof("行情异动消息推送成功，ID: %d ,推送总数: %d , 成功: %d , 失败: %d ",
		marketChanges.MarketChangesID, result.SendTotal, result.SuccessCount, result.FailCount)

	return nil
}

// GetMarketChangesTradingSectionModels 获取公告关联板块模型数据，用于新增关联数据
func GetMarketChangesTradingSectionModels(ctx context.Context, tradeSectionIDs []string, marketChangesID int64) ([]*tsmodel.MarketChangesTradingSection, error) {
	tsIDs := make([]int64, 0, len(tradeSectionIDs))
	for _, tsIDStr := range tradeSectionIDs {
		tsID, err := util.Str2Int64(tsIDStr)
		if err != nil {
			return nil, err
		}
		tsIDs = append(tsIDs, tsID)
	}
	// 查询对应的板块分区数据
	tsSchema := tsrepo.GetQuery().TradingSection
	tsQw := search.NewQueryBuilder().In(tsSchema.ID, tsIDs).Build()
	tradingSections, err := tsrepo.NewTradingSectionRepo(tsSchema.WithContext(ctx)).SelectList(tsQw)
	if err != nil {
		return nil, err
	}
	annTsModels := make([]*tsmodel.MarketChangesTradingSection, 0)
	for _, ts := range tradingSections {
		annTsModels = append(annTsModels, &tsmodel.MarketChangesTradingSection{
			MarketChangesID:    marketChangesID,
			TradingSectionID:   ts.ID,
			TradingSectionName: ts.Name,
		})
	}

	return annTsModels, nil
}

func GetTradingSectionMap(ctx context.Context, mcIDs []int64) (map[int64][]*define.GetAdminTradingSectionData, error) {
	// 批量查询板块信息
	mctsSchema := tsrepo.GetQuery().MarketChangesTradingSection
	mctsQw := search.NewQueryBuilder().In(mctsSchema.MarketChangesID, mcIDs).Build()
	annTradingSections, err := tsrepo.NewMarketChangesTradingSectionRepo(mctsSchema.WithContext(ctx)).SelectList(mctsQw)
	if err != nil {
		return nil, err
	}
	tradingSectionMap := make(map[int64][]*define.GetAdminTradingSectionData)
	for _, ats := range annTradingSections {
		if _, ok := tradingSectionMap[ats.MarketChangesID]; ok {
			tradingSectionMap[ats.MarketChangesID] = append(tradingSectionMap[ats.MarketChangesID], &define.GetAdminTradingSectionData{
				ID:   ats.TradingSectionID,
				Name: ats.TradingSectionName,
			})
		} else {
			tradingSectionMap[ats.MarketChangesID] = []*define.GetAdminTradingSectionData{
				{
					ID:   ats.TradingSectionID,
					Name: ats.TradingSectionName,
				},
			}
		}
	}

	return tradingSectionMap, nil
}
