package define

import (
	"time"

	"app_service/apps/business/operation_announcement/define/enums"
	"app_service/pkg/pagination"
)

type (
	GetAdminTradingSectionData struct {
		ID   int64  `json:"id,string"` // 板块 id
		Name string `json:"name"`      // 板块名称
	}
)

// 运营公告列表相关结构体
type (
	GetOperationAnnouncementAdminListReq struct {
		pagination.Pagination
		ID                 int64                               `form:"id" json:"id,string"`                                 // 运营公告ID
		Title              string                              `form:"title" json:"title"`                                  // 运营公告标题
		CategoryID         int64                               `form:"category_id" json:"category_id,string"`               // 分类ID
		Status             enums.OperationAnnouncementStatus   `form:"status" json:"status"`                                // 状态
		TimeType           enums.OperationAnnouncementTimeType `form:"time_type" json:"time_type"`                          // 时间类型
		StartTime          time.Time                           `form:"start_time" json:"start_time"`                        // 开始时间
		EndTime            time.Time                           `form:"end_time" json:"end_time"`                            // 结束时间
		ItemId             string                              `form:"item_id" json:"item_id"`                              // 商品ID
		ItemName           string                              `form:"item_name" json:"item_name"`                          // 商品名称
		ChannelId          string                              `form:"channel_id" json:"channel_id"`                        // 渠道ID
		CreatedBy          string                              `form:"created_by" json:"created_by"`                        // 创建人
		TradingSectionID   int64                               `form:"trading_section_id" json:"trading_section_id,string"` // 板块 id
		TradingSectionName string                              `form:"trading_section_name" json:"trading_section_name"`    // 板块名称
	}

	GetItemIdInfoList struct {
		ID       string `json:"id"`        // ID
		ItemId   string `json:"item_id"`   // 商品ID
		ItemName string `json:"item_name"` // 商品名称
		ImageUrl string `json:"image_url"` // 商品图片
	}

	GetOperationAnnouncementAdminListData struct {
		ID             int64                                       `json:"id,string"`          // 运营公告ID
		Title          string                                      `json:"title"`              // 运营公告标题
		Content        string                                      `json:"content"`            // 运营公告内容
		CategoryID     int64                                       `json:"category_id,string"` // 分类ID
		CategoryInfo   *GetOperationAnnCategoryAdminLessDetailResp `json:"category_info"`      // 分类信息
		Priority       int32                                       `json:"priority"`           // 优先级
		Status         enums.OperationAnnouncementStatus           `json:"status"`             // 状态
		PublishTime    *time.Time                                  `json:"publish_time"`       // 发布时间
		ItemIds        []string                                    `json:"item_ids"`           // 关联商品ID列表
		ItemIdInfoList []*GetItemIdInfoList                        `json:"item_id_info_list"`  // 关联商品列表
		ChannelIds     []string                                    `json:"channel_ids"`        // 关联渠道ID列表
		PublishType    enums.OperationAnnouncementPublishType      `json:"publish_type"`       // 发布类型
		CreatedBy      string                                      `json:"created_by"`         // 创建人
		//CreatorInfo   *GetUserInfoAdminResp             `json:"creator_info"`       // 创建人信息
		AdIds           []string                      `json:"ad_ids"`           // 关联广告ID列表
		TradingSections []*GetAdminTradingSectionData `json:"trading_sections"` // 关联板块列表
		CreatedAt       time.Time                     `json:"created_at"`       // 创建时间
		UpdatedAt       time.Time                     `json:"updated_at"`       // 更新时间
	}

	GetOperationAnnouncementAdminListResp struct {
		List  []*GetOperationAnnouncementAdminListData `json:"list"`
		Total int64                                    `json:"total"`
	}
)

// 运营公告详情相关结构体
type (
	GetOperationAnnouncementAdminDetailReq struct {
		ID int64 `form:"id" json:"id,string" binding:"required"` // 运营公告ID
	}

	GetOperationAnnouncementAdminDetailResp struct {
		ID              int64                                       `json:"id,string"`          // 运营公告ID
		Title           string                                      `json:"title"`              // 运营公告标题
		Content         string                                      `json:"content"`            // 运营公告内容
		CategoryID      int64                                       `json:"category_id,string"` // 分类ID
		CategoryInfo    *GetOperationAnnCategoryAdminLessDetailResp `json:"category_info"`      // 分类信息
		Priority        int32                                       `json:"priority"`           // 优先级
		Status          enums.OperationAnnouncementStatus           `json:"status"`             // 状态
		PublishTime     *time.Time                                  `json:"publish_time"`       // 发布时间
		ItemIds         []string                                    `json:"item_ids"`           // 关联商品ID列表
		ItemIdInfoList  []*GetItemIdInfoList                        `json:"item_id_info_list"`  // 关联商品列表
		ChannelIds      []string                                    `json:"channel_ids"`        // 关联渠道ID列表
		PublishType     enums.OperationAnnouncementPublishType      `json:"publish_type"`       // 发布类型
		MessagePush     enums.OperationAnnouncementMessagePushEnum  `json:"message_push"`       // 消息推送【1:推送, 2:不推送】
		CreatedBy       string                                      `json:"created_by"`         // 创建人
		TradingSections []*GetAdminTradingSectionData               `json:"trading_sections"`   // 关联板块列表
		CreatedAt       time.Time                                   `json:"created_at"`         // 创建时间
		UpdatedAt       time.Time                                   `json:"updated_at"`         // 更新时间
	}
)

// 新增运营公告相关结构体
type (
	AddOperationAnnouncementReq struct {
		Title             string                                     `json:"title" binding:"required"`                  // 运营公告标题
		Content           string                                     `json:"content" binding:"required"`                // 运营公告内容
		CategoryID        int64                                      `json:"category_id,string" binding:"required"`     // 分类ID
		Priority          int32                                      `json:"priority"`                                  // 优先级
		PublishTime       *time.Time                                 `json:"publish_time"`                              // 发布时间
		ItemIds           []string                                   `json:"item_ids"`                                  // 关联商品ID列表
		ChannelIds        []string                                   `json:"channel_ids"  binding:"required"`           // 关联渠道ID列表
		PublishType       enums.OperationAnnouncementPublishType     `json:"publish_type" binding:"required"`           // 发布类型
		MessagePush       enums.OperationAnnouncementMessagePushEnum `json:"message_push" binding:"required,oneof=1 2"` // 消息推送【1:推送, 2:不推送】
		TradingSectionIDs []string                                   `json:"trading_section_ids"`                       // 板块分区 id 列表
	}

	AddOperationAnnouncementResp struct {
		ID int64 `json:"id"` // 运营公告ID
	}
)

// 编辑运营公告相关结构体
type (
	EditOperationAnnouncementReq struct {
		ID                int64                                      `json:"id,string" binding:"required"`              // 运营公告ID
		Title             string                                     `json:"title" binding:"required"`                  // 运营公告标题
		Content           string                                     `json:"content" binding:"required"`                // 运营公告内容
		CategoryID        int64                                      `json:"category_id,string" binding:"required"`     // 分类ID
		Priority          int32                                      `json:"priority"`                                  // 优先级
		PublishTime       *time.Time                                 `json:"publish_time"`                              // 发布时间
		ItemIds           []string                                   `json:"item_ids"`                                  // 关联商品ID列表
		ChannelIds        []string                                   `json:"channel_ids"  binding:"required"`           // 关联渠道ID列表
		PublishType       enums.OperationAnnouncementPublishType     `json:"publish_type" binding:"required"`           // 发布类型
		MessagePush       enums.OperationAnnouncementMessagePushEnum `json:"message_push" binding:"required,oneof=1 2"` // 消息推送【1:推送, 2:不推送】
		TradingSectionIDs []string                                   `json:"trading_section_ids"`                       // 板块分区 id 列表
	}

	EditOperationAnnouncementResp struct {
		ID int64 `json:"id,string"` // 运营公告ID
	}
)

// 编辑运营公告状态相关结构体
type (
	EditOperationAnnouncementStatusReq struct {
		ID     int64                             `json:"id,string" binding:"required"` // 运营公告ID
		Status enums.OperationAnnouncementStatus `json:"status" binding:"required"`    // 状态
	}

	EditOperationAnnouncementStatusResp struct {
		ID int64 `json:"id,string"` // 运营公告ID
	}
)

// 编辑运营公告优先级相关结构体
type (
	EditOperationAnnouncementPriorityReq struct {
		ID       int64 `json:"id,string" binding:"required"` // 运营公告ID
		Priority int32 `json:"priority" binding:"required"`  // 优先级
	}

	EditOperationAnnouncementPriorityResp struct {
		ID int64 `json:"id,string"` // 运营公告ID
	}
)

// 编辑运营公告关联广告ID相关结构体
type (
	EditOperationAnnouncementAdIdsReq struct {
		ID    int64    `json:"id,string" binding:"required"` // 运营公告ID
		AdIds []string `json:"ad_ids" binding:"required"`    // 关联广告ID列表
	}

	EditOperationAnnouncementAdIdsResp struct {
		ID int64 `json:"id,string"` // 运营公告ID
	}
)

// 删除运营公告相关结构体
type (
	DelOperationAnnouncementReq struct {
		ID int64 `json:"id,string" binding:"required"` // 运营公告ID
	}

	DelOperationAnnouncementResp struct {
		ID int64 `json:"id,string"` // 运营公告ID
	}
)

// 运营公告分类列表相关结构体
type (
	GetOperationAnnCategoryAdminListReq struct {
		pagination.Pagination
		//Name   string `form:"name" json:"name"`     // 分类名称
		//Status int32  `form:"status" json:"status"` // 状态
	}

	GetOperationAnnCategoryAdminListData struct {
		ID        int64                            `json:"id,string"`  // 分类ID
		Name      string                           `json:"name"`       // 分类名称
		AnnNum    int32                            `json:"ann_num"`    // 运营公告数量
		Priority  int32                            `json:"priority"`   // 优先级
		Status    enums.OperationAnnCategoryStatus `json:"status"`     // 状态
		CreatedAt time.Time                        `json:"created_at"` // 创建时间
		UpdatedAt time.Time                        `json:"updated_at"` // 更新时间
	}

	GetOperationAnnCategoryAdminListResp struct {
		List  []*GetOperationAnnCategoryAdminListData `json:"list"`
		Total int64                                   `json:"total"`
	}
)

// 运营公告分类详情相关结构体
type (
	GetOperationAnnCategoryAdminDetailReq struct {
		ID int64 `form:"id" json:"id,string" binding:"required"` // 分类ID
	}

	GetOperationAnnCategoryAdminDetailResp struct {
		ID              int64                            `json:"id,string"`        // 分类ID
		Name            string                           `json:"name"`             // 分类名称
		TextColor       string                           `json:"text_color"`       // 文字颜色
		BackgroundColor string                           `json:"background_color"` // 背景颜色
		Priority        int32                            `json:"priority"`         // 优先级
		Status          enums.OperationAnnCategoryStatus `json:"status"`           // 状态
		CreatedAt       time.Time                        `json:"created_at"`       // 创建时间
		UpdatedAt       time.Time                        `json:"updated_at"`       // 更新时间
	}

	GetOperationAnnCategoryAdminLessDetailResp struct {
		ID   int64  `json:"id,string"` // 分类ID
		Name string `json:"name"`      // 分类名称
	}
)

// 新增运营公告分类相关结构体
type (
	AddOperationAnnCategoryReq struct {
		Name            string `json:"name" binding:"required"`             // 分类名称
		TextColor       string `json:"text_color" binding:"required"`       // 文字颜色
		BackgroundColor string `json:"background_color" binding:"required"` // 背景颜色
		Priority        int32  `json:"priority" binding:"required"`         // 优先级
	}

	AddOperationAnnCategoryResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)

// 编辑运营公告分类相关结构体
type (
	EditOperationAnnCategoryReq struct {
		ID              int64  `json:"id,string" binding:"required"`        // 分类ID
		Name            string `json:"name" binding:"required"`             // 分类名称
		TextColor       string `json:"text_color" binding:"required"`       // 文字颜色
		BackgroundColor string `json:"background_color" binding:"required"` // 背景颜色
		Priority        int32  `json:"priority" binding:"required"`         // 优先级
	}

	EditOperationAnnCategoryResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)

// 编辑运营公告分类优先级相关结构体
type (
	EditOperationAnnCategoryPriorityReq struct {
		ID       int64 `json:"id,string" binding:"required"` // 分类ID
		Priority int32 `json:"priority" binding:"required"`  //  优先级
	}

	EditOperationAnnCategoryPriorityResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)

// 删除运营公告分类相关结构体
type (
	DelOperationAnnCategoryReq struct {
		ID int64 `json:"id,string" binding:"required"` // 分类ID
	}

	DelOperationAnnCategoryResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)
