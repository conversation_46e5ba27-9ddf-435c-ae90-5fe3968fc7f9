package logic

import (
	notidefine "app_service/apps/business/notification/define"
	notienums "app_service/apps/business/notification/define/enums"
	notifacade "app_service/apps/business/notification/facade"
	"app_service/apps/business/operation_announcement/dal/model"
	"app_service/apps/business/operation_announcement/define"
	"app_service/apps/business/operation_announcement/define/enums"
	"app_service/apps/business/operation_announcement/repo"
	tsmodel "app_service/apps/business/trading_section/dal/model"
	tsrepo "app_service/apps/business/trading_section/repo"
	"app_service/apps/platform/common/constant"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	"encoding/json"
	"fmt"

	log "e.coding.net/g-dtay0385/common/go-logger"

	"gorm.io/datatypes"
)

// UnmarshalIds 解析ids
func UnmarshalIds(Ids datatypes.JSON) ([]string, error) {
	var data []string
	if err := json.Unmarshal(Ids, &data); err != nil {
		return nil, err
	}
	return data, nil
}

// GetCategoryAnnCounts 获取分类运营公告数量
// 返回 map[categoryID]count
func GetCategoryAnnCounts(ctx context.Context, categoryIds []int64) (map[int64]int32, error) {
	annSchema := repo.GetQuery().OperationAnnouncement
	annRepo := repo.NewOperationAnnouncementRepo(annSchema.WithContext(ctx))

	// 构建查询条件
	builder := search.NewQueryBuilder()
	if len(categoryIds) > 0 {
		builder = builder.In(annSchema.CategoryID, categoryIds)
	}

	// 只查询CategoryID字段，提升查询效率
	builder = builder.Select(annSchema.CategoryID)

	// 获取运营公告列表
	announcements, err := annRepo.SelectList(builder.Build())
	if err != nil {
		return nil, err
	}

	// 统计每个分类的运营公告数量
	categoryCountMap := make(map[int64]int32)
	for _, ann := range announcements {
		categoryCountMap[ann.CategoryID]++
	}

	return categoryCountMap, nil
}

// GetPublishedAndChannelCondition 获取已发布和渠道过滤条件
func GetPublishedAndChannelCondition(ctx context.Context) (string, []interface{}) {
	channel, _ := ctx.Value(constant.AppChannel).(string)
	where := "status = ? AND (JSON_CONTAINS(channel_ids, ?) OR JSON_CONTAINS(channel_ids, '\"all\"'))"
	args := []interface{}{
		enums.OperationAnnouncementStatusPublished.Val(),
		fmt.Sprintf("\"%s\"", channel),
	}
	return where, args
}

// PushOperationAnnouncementMessage 推送运营方公告消息
func PushOperationAnnouncementMessage(ctx context.Context, ann *model.OperationAnnouncement) error {
	audienceType := notienums.PushAudienceTypeChannel
	// 解析ChannelIds
	var channelIds []string
	if len(ann.ChannelIds) > 0 {
		chIds, err := UnmarshalIds(ann.ChannelIds)
		if err != nil {
			return err
		}
		channelIds = chIds
	}
	for _, ch := range channelIds {
		if ch == "all" {
			audienceType = notienums.PushAudienceTypeAll
			break
		}
	}
	extras := make(map[string]interface{})
	pageURL := "ojb://message_list?type=1" // 运营方公告列表页
	if ann.Content != "" {
		// 有内容就跳转到详情页
		baseURL := "https://sit-wcjs-acp.ahbq.com.cn"
		if global.GlobalConfig.Service.Env == global.EnvProd {
			baseURL = "https://wcjs-acp.ahbq.com.cn"
		}
		pageURL = fmt.Sprintf("ojb://webview_h5?url=%s/messageDetail?id=%d&type=operation",
			baseURL, ann.OperationAnnouncementID)
	}
	extras["url"] = pageURL
	message := notidefine.PushMessage{
		Title:         "运营方公告",
		Content:       ann.Title,
		AudienceType:  audienceType,
		ChannelIDs:    channelIds,
		AndroidExtras: extras,
		IOSExtras:     extras,
	}
	relateInfo := notidefine.PushRelateInfo{
		RelateType:  notienums.PushRelateTypeOperationAnnouncement,
		RelateScene: notienums.PushRelateScenePublish,
		RelateID:    util.StrVal(ann.OperationAnnouncementID),
	}
	result, err := notifacade.PushMessage(ctx, message, relateInfo)
	if err != nil {
		return err
	}

	log.Ctx(ctx).Infof("运营方公告消息推送成功，ID: %d ,推送总数: %d , 成功: %d , 失败: %d ",
		ann.OperationAnnouncementID, result.SendTotal, result.SuccessCount, result.FailCount)

	return nil
}

// GetOperAnnTradingSectionModels 获取公告关联板块模型数据，用于新增关联数据
func GetOperAnnTradingSectionModels(ctx context.Context, tradeSectionIDs []string, operAnnID int64) ([]*tsmodel.OperAnnTradingSection, error) {
	tsIDs := make([]int64, 0, len(tradeSectionIDs))
	for _, tsIDStr := range tradeSectionIDs {
		tsID, err := util.Str2Int64(tsIDStr)
		if err != nil {
			return nil, err
		}
		tsIDs = append(tsIDs, tsID)
	}
	// 查询对应的板块分区数据
	tsSchema := tsrepo.GetQuery().TradingSection
	tsQw := search.NewQueryBuilder().In(tsSchema.ID, tsIDs).Build()
	tradingSections, err := tsrepo.NewTradingSectionRepo(tsSchema.WithContext(ctx)).SelectList(tsQw)
	if err != nil {
		return nil, err
	}
	annTsModels := make([]*tsmodel.OperAnnTradingSection, 0)
	for _, ts := range tradingSections {
		annTsModels = append(annTsModels, &tsmodel.OperAnnTradingSection{
			OperAnnID:          operAnnID,
			TradingSectionID:   ts.ID,
			TradingSectionName: ts.Name,
		})
	}

	return annTsModels, nil
}

func GetTradingSectionMap(ctx context.Context, operAnnIDs []int64) (map[int64][]*define.GetAdminTradingSectionData, error) {
	// 批量查询板块信息
	oatsSchema := tsrepo.GetQuery().OperAnnTradingSection
	oatsQw := search.NewQueryBuilder().In(oatsSchema.OperAnnID, operAnnIDs).Build()
	annTradingSections, err := tsrepo.NewOperAnnTradingSectionRepo(oatsSchema.WithContext(ctx)).SelectList(oatsQw)
	if err != nil {
		return nil, err
	}
	tradingSectionMap := make(map[int64][]*define.GetAdminTradingSectionData)
	for _, ats := range annTradingSections {
		if _, ok := tradingSectionMap[ats.OperAnnID]; ok {
			tradingSectionMap[ats.OperAnnID] = append(tradingSectionMap[ats.OperAnnID], &define.GetAdminTradingSectionData{
				ID:   ats.TradingSectionID,
				Name: ats.TradingSectionName,
			})
		} else {
			tradingSectionMap[ats.OperAnnID] = []*define.GetAdminTradingSectionData{
				{
					ID:   ats.TradingSectionID,
					Name: ats.TradingSectionName,
				},
			}
		}
	}

	return tradingSectionMap, nil
}
