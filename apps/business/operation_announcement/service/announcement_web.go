package service

import (
	"app_service/apps/business/operation_announcement/dal/model"
	"app_service/apps/business/operation_announcement/define"
	"app_service/apps/business/operation_announcement/define/enums"
	"app_service/apps/business/operation_announcement/repo"
	"app_service/apps/business/operation_announcement/service/logic"
	"app_service/pkg/search"
	"fmt"
	"strings"
	"time"
)

// GetOperationAnnouncementWebList 运营公告列表查询，仅查已发布运营公告，按 web 端结构组装响应。
func (s *Service) GetOperationAnnouncementWebList(req *define.GetOperationAnnouncementWebListReq) (*define.GetOperationAnnouncementWebListResp, error) {
	db := repo.GetDB().WithContext(s.ctx)

	// 使用公共条件
	where, args := logic.GetPublishedAndChannelCondition(s.ctx)
	db = db.Where(where, args...)

	if req.KeyWord != "" {
		keyword := "%" + req.KeyWord + "%"
		// 搜索标题和内容、板块名称
		db = db.Where("title LIKE ? OR content LIKE ? OR EXISTS (SELECT 1 FROM oper_ann_trading_section oats WHERE oats.oper_ann_id=operation_announcement.operation_announcement_id AND oats.trading_section_name LIKE ? )", keyword, keyword, keyword)
	}
	if req.CategoryID != 0 {
		db = db.Where("category_id = ?", req.CategoryID)
	}
	if req.ItemId != "" {
		db = db.Where("JSON_CONTAINS(item_ids, ?)", "\""+req.ItemId+"\"")
	}

	// 排序、分页
	if req.OrderBy != "" && req.SortOrder != "" {
		// 传了排序字段，按照请求的排序参数排序
		isDesc := strings.ToLower(req.SortOrder) == "desc"
		orderBy := strings.ToLower(req.OrderBy)
		switch orderBy {
		case "publish_time":
			if isDesc {
				db = db.Order("publish_time DESC")
			} else {
				db = db.Order("publish_time ASC")
			}
		}
	} else {
		db = db.Order("priority DESC, publish_time DESC")
	}
	offset := (req.GetPage() - 1) * req.GetPageSize()
	if offset < 0 {
		offset = 0
	}
	var list []*model.OperationAnnouncement
	err := db.Offset(offset).Limit(req.GetPageSize()).Find(&list).Error
	if err != nil {
		return nil, err
	}
	resp := &define.GetOperationAnnouncementWebListResp{
		List: make([]*define.GetOperationAnnouncementWebListData, 0),
	}
	if len(list) == 0 {
		return resp, nil
	}

	// 批量查分类
	var categoryIds []int64
	for _, v := range list {
		categoryIds = append(categoryIds, v.CategoryID)
	}
	categorySchema := repo.GetQuery().OperationAnnCategory
	categories, _, err := repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectPage(
		search.NewQueryBuilder().In(categorySchema.OperationAnnCategoryID, categoryIds).Build(),
		1, len(categoryIds),
	)
	if err != nil {
		return nil, err
	}
	categoryMap := make(map[int64]*model.OperationAnnCategory)
	for _, category := range categories {
		categoryMap[category.OperationAnnCategoryID] = category
	}

	// 获取当前时间（本地时区）
	now := time.Now()
	formatNow := now.Format("2006-01-02T15:04:05.000Z07:00")

	for _, v := range list {
		var categoryInfo *define.GetOperationAnnCategoryWebLessDetailResp
		if category, ok := categoryMap[v.CategoryID]; ok {
			categoryInfo = &define.GetOperationAnnCategoryWebLessDetailResp{
				ID:              category.OperationAnnCategoryID,
				Name:            category.Name,
				TextColor:       category.TextColor,
				BackgroundColor: category.BackgroundColor,
			}
		}
		resp.List = append(resp.List, &define.GetOperationAnnouncementWebListData{
			ID:           v.OperationAnnouncementID,
			Title:        v.Title,
			CategoryID:   v.CategoryID,
			CategoryInfo: categoryInfo,
			PublishTime:  v.PublishTime,
			CurrentTime:  formatNow,
		})
	}
	resp.HasMore = len(resp.List) == req.GetPageSize()
	return resp, nil
}

// GetOperationAnnouncementWebDetail 运营公告详情查询，仅查已发布运营公告。
func (s *Service) GetOperationAnnouncementWebDetail(req *define.GetOperationAnnouncementWebDetailReq) (*define.GetOperationAnnouncementWebDetailResp, error) {
	annSchema := repo.GetQuery().OperationAnnouncement
	builder := search.NewQueryBuilder().Eq(annSchema.OperationAnnouncementID, req.ID)
	announcement, err := repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}
	if announcement.Status != enums.OperationAnnouncementStatusPublished.Val() {
		return nil, define.AH220006Err
	}

	// 解析 ItemIds
	var itemIds []string
	if announcement.ItemIds != nil {
		itemIds, err = logic.UnmarshalIds(*announcement.ItemIds)
		if err != nil {
			return nil, err
		}
	}

	// 查分类信息
	categorySchema := repo.GetQuery().OperationAnnCategory
	category, err := repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectOne(
		search.NewQueryBuilder().Eq(categorySchema.OperationAnnCategoryID, announcement.CategoryID).Build(),
	)
	if err != nil {
		return nil, err
	}
	var categoryInfo *define.GetOperationAnnCategoryWebLessDetailResp
	if category != nil {
		categoryInfo = &define.GetOperationAnnCategoryWebLessDetailResp{
			ID:              category.OperationAnnCategoryID,
			Name:            category.Name,
			TextColor:       category.TextColor,
			BackgroundColor: category.BackgroundColor,
		}
	}

	return &define.GetOperationAnnouncementWebDetailResp{
		ID:           announcement.OperationAnnouncementID,
		Title:        announcement.Title,
		Content:      announcement.Content,
		CategoryID:   announcement.CategoryID,
		CategoryInfo: categoryInfo,
		PublishTime:  announcement.PublishTime,
		ItemIds:      itemIds,
	}, nil
}

// GetOperationAnnCategoryWebList 运营公告分类列表查询，仅查有效分类。
func (s *Service) GetOperationAnnCategoryWebList(req *define.GetOperationAnnCategoryWebListReq) (*define.GetOperationAnnCategoryWebListResp, error) {
	var categories []model.OperationAnnCategory
	db := repo.GetDB().WithContext(s.ctx)

	// 获取公共条件
	where, args := logic.GetPublishedAndChannelCondition(s.ctx)

	// 构建 EXISTS 子查询
	existsWhere := fmt.Sprintf(`
		EXISTS (
			SELECT 1 FROM operation_announcement
			WHERE operation_announcement.category_id = operation_ann_category.operation_ann_category_id
				AND %s
		)
	`, where)

	err := db.
		Table("operation_ann_category").
		Where("status = ?", enums.OperationAnnCategoryStatusEnable.Val()).
		Where(existsWhere, args...).
		Order("priority DESC, created_at DESC").
		Limit(req.GetPageSize()).
		Offset((req.GetPage() - 1) * req.GetPageSize()).
		Find(&categories).Error

	if err != nil {
		return nil, err
	}
	resp := &define.GetOperationAnnCategoryWebListResp{
		List: make([]*define.GetOperationAnnCategoryWebListData, 0),
	}
	if len(categories) == 0 {
		return resp, nil
	}

	for _, v := range categories {
		resp.List = append(resp.List, &define.GetOperationAnnCategoryWebListData{
			ID:   v.OperationAnnCategoryID,
			Name: v.Name,
		})
	}
	resp.HasMore = len(resp.List) == req.GetPageSize()
	return resp, nil
}
