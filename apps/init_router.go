package apps

import (
	_ "app_service/apps/business/announcement/router"
	_ "app_service/apps/business/bonus_mall/router"
	_ "app_service/apps/business/card_community/router"
	_ "app_service/apps/business/invite_reward/router"
	_ "app_service/apps/business/market_changes/router"
	_ "app_service/apps/business/notification/router"
	_ "app_service/apps/business/operation_announcement/router"
	_ "app_service/apps/business/story/router"
	_ "app_service/apps/business/synthesis/router"
	_ "app_service/apps/business/trade/router"
	_ "app_service/apps/business/trading_section/router"
	_ "app_service/apps/business/yc/router"
	_ "app_service/apps/platform/asset/router"
	_ "app_service/apps/platform/common/router"
	_ "app_service/apps/platform/system/router"
	_ "app_service/apps/platform/user/router"
	"app_service/global"
	"net/http"

	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// Check 健康检查
func Check(ctx *gin.Context) {
	ctx.JSON(http.StatusOK, "ok")
}

// Init 初始化路由
func Init() *gin.Engine {
	gin.SetMode(gin.ReleaseMode)

	router := gin.New()
	router.ContextWithFallback = true
	// 注册路由中间件
	router.Use(g.Cors())

	// 健康检查路由
	router.GET("/check", Check)

	// 定义swagger 的路径，首页是 /swagger/index.html
	router.GET("/swagger/app_service/*any", ginSwagger.WrapHandler(swaggerFiles.Handler, ginSwagger.InstanceName("app_service")))

	global.SetEngine(router)

	return router
}
